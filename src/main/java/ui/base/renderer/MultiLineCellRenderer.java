package ui.base.renderer;

import lombok.Getter;

import javax.swing.*;
import javax.swing.border.MatteBorder;
import javax.swing.table.TableCellRenderer;
import java.awt.*;

@Getter
public class MultiLineCellRenderer extends JTextArea implements Table<PERSON>ellRenderer, ColorCellRenderer {

    private final ColorRender colorRender = new ColorRender();

    public MultiLineCellRenderer() {
        setLineWrap(false);    //设置为false才不影响自适应列宽的方法
        setWrapStyleWord(true);
        setOpaque(true);
    }

    @Override
    public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
        Color bgColor;
        if (colorRender.getColor(row) != null) {
            bgColor = colorRender.getColor(row);
        } else {
            bgColor = colorRender.getColor(row, column);
        }

        if (isSelected) {
            if (bgColor == null) {
                setBackground(table.getSelectionBackground());
            } else {
                setBackground(table.getSelectionBackground());
            }
        } else {
            setBackground(bgColor);
        }
        setForeground(table.getForeground());
        setFont(table.getFont());
        setText(value == null ? "" : value.toString());

        setBorder(new MatteBorder(1, 0, 1, 1, table.getGridColor()));
        
        // 设置表格单元格的首选宽度不被限制，允许自由调整列宽而不影响其他列
        if (table.getParent() instanceof JViewport) {
            table.setPreferredScrollableViewportSize(new Dimension(Integer.MAX_VALUE, table.getPreferredScrollableViewportSize().height));
            // 禁止自动调整其他列宽度
            table.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
        }
        
        return this;
    }

}
 
