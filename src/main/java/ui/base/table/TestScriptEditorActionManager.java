package ui.base.table;


import common.utils.CopyUtils;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import sdk.base.execution.ExecutionStatus;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationGroup;
import sdk.base.operation.OperationMethod;
import sdk.constants.methods.CommonMethods;
import sdk.domain.OperationLabel;
import sdk.entity.OperationTargetHolder;
import ui.base.dialogs.SpinnerDialog;
import ui.base.memo.Memento;
import ui.base.memo.OperationCareTaker;
import ui.layout.right.components.testscript.fileview.TestScriptTableFileView;
import ui.layout.right.components.testscript.scriptview.LabelConfigDialog;
import ui.layout.right.components.testscript.scriptview.TestScriptEditorTable;
import ui.layout.right.components.testscript.toolkit.AssistantActionHelper;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 脚本编辑器动作管理器
 */
@Getter
@Slf4j
public class TestScriptEditorActionManager extends TableActionManager<Operation> {

    private final AnnotationActionListener annotationActionListener = new AnnotationActionListener();

    private final ModifyDeviceIndexActionListener modifyDeviceIndexActionListener = new ModifyDeviceIndexActionListener();
    private final ModifyChannelIndexActionListener modifyChannelIndexActionListener = new ModifyChannelIndexActionListener();
    private static final String ANNOTATION_MARK = "//";
    private static final String BACKGROUND_MARK = "@";

    public TestScriptEditorActionManager(DefaultTable<Operation> defaultTable) {
        super(defaultTable);
    }

    public ExecuteCurrentOperationsActionListener getExecuteCurrentOperationsActionListener(MainModel mainModel) {
        return new ExecuteCurrentOperationsActionListener(mainModel);
    }

    public ExecuteOperationsActionListener getExecuteOperationsActionListener(MainModel mainModel) {
        return new ExecuteOperationsActionListener(mainModel);
    }


    public AddNoteActionListener getAddNoteActionListener(MainModel mainModel) {
        return new AddNoteActionListener();
    }

    public CancelNoteActionListener getCancelNoteActionListener(MainModel mainModel) {
        return new CancelNoteActionListener();
    }

    public IfSuccessActionListener getIfSuccessActionListener(MainModel mainModel) {
        return new IfSuccessActionListener(mainModel);
    }

    public CombineOperationGroupActionListener getCombineOperationGroupActionListener(MainModel mainModel) {
        return new CombineOperationGroupActionListener(mainModel);
    }

    public CreateOperationGroupActionListener getCreateOperationGroupActionListener(MainModel mainModel) {
        return new CreateOperationGroupActionListener(mainModel);
    }

    public LoadOperationGroupActionListener getLoadOperationGroupActionListener(MainModel mainModel) {
        return new LoadOperationGroupActionListener(mainModel);
    }

    public FailRetryActionListener getFailRetryActionListener(MainModel mainModel) {
        return new FailRetryActionListener(mainModel);
    }

    public ActionListener getMoveToBackgroundTaskActionListener(MainModel mainModel) {
        return new MoveToBackgroundTaskActionListener(mainModel);
    }

    public WrappedInnerLoopActionListener getWrappedInnerLoopActionListener(MainModel mainModel) {
        return new WrappedInnerLoopActionListener(mainModel);
    }

    public LabelConfigActionListener getLabelConfigActionListener(MainModel mainModel){
        return new LabelConfigActionListener(mainModel);
    }

    public SetLabelActionListener getSetLabelActionListener(MainModel mainModel, OperationLabel operationLabel){
        return new SetLabelActionListener(mainModel, operationLabel);
    }

    public CancelLabelActionListener getCancelLabelActionListener(MainModel mainModel){
        return new CancelLabelActionListener(mainModel);
    }

    public class CombineOperationGroupActionListener implements ActionListener {
        private final MainModel mainModel;

        public CombineOperationGroupActionListener(MainModel mainModel) {
            this.mainModel = mainModel;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            String groupName = JOptionPane.showInputDialog("输入动作组合名称:");
            if (groupName != null) {
                List<OperationGroup> operationGroups = OperationTargetHolder.getOperationGroupKit().loadOperationGroups(mainModel.getAppInfo().getProject());
                for (OperationGroup operationGroup : operationGroups) {
                    if (operationGroup.getGroupName().equals(groupName)) {
                        int userOption = JOptionPane.showConfirmDialog(defaultTable, "该动作组合已存在，是否覆盖");
                        if (userOption == JOptionPane.OK_OPTION) {
                            break;
                        } else {
                            return;
                        }
                    }
                }
                List<Operation> operationList = defaultTable.getSelectedRowsData();
                OperationGroup operationGroup = new OperationGroup();
                operationGroup.setGroupName(groupName);
                operationGroup.setProjectName(mainModel.getAppInfo().getProject());
                operationGroup.setOperationList(operationList);
                // 将添加到组合的步骤从表格删除
                int[] rows = defaultTable.getSelectedRows();
                rows = Arrays.stream(rows).boxed().sorted(((o1, o2) -> o2 - o1)).mapToInt(int1 -> int1).toArray();
                defaultTable.deleteRowsActivated(rows);
                mainModel.getOperationModel().updateOperationFinish();

                // 添加动作组合
                OperationTargetHolder.getOperationGroupKit().addOperationGroup(operationGroup);
                mainModel.getOperationGroupModel().addOperationGroup(operationGroup);

                // 将新添加的动作步骤添加到表格中
                Operation operation = new Operation();
                operation.setOperationMethod(CommonMethods.loadOperationGroup);
                operation.setOperationObject(operationGroup.getGroupName());
                mainModel.getOperationModel().updateOperation(operation);

                // 切换到步骤组合的编辑界面
                mainModel.getOperationGroupModel().loadOperationGroup(groupName);
            }
        }
    }

    @Data
    public class CreateOperationGroupActionListener implements ActionListener {
        private final MainModel mainModel;

        private String presetGroupName;

        private boolean moveToHead;

        public CreateOperationGroupActionListener(MainModel mainModel) {
            this.mainModel = mainModel;
            presetGroupName = null;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            String groupName = presetGroupName == null ? JOptionPane.showInputDialog("输入新建动作组合名称:") : presetGroupName;
            if (groupName != null) {
                List<OperationGroup> operationGroups = OperationTargetHolder.getOperationGroupKit().loadOperationGroups(mainModel.getAppInfo().getProject());
                for (OperationGroup operationGroup : operationGroups) {
                    if (operationGroup.getGroupName().equals(groupName)) {
                        int userOption = JOptionPane.showConfirmDialog(defaultTable, "该动作组合已存在，是否覆盖");
                        if (userOption == JOptionPane.OK_OPTION) {
                            break;
                        } else {
                            return;
                        }
                    }
                }

                // 新建步骤组合
                OperationGroup operationGroup = new OperationGroup();
                operationGroup.setGroupName(groupName);
                operationGroup.setProjectName(mainModel.getAppInfo().getProject());
                operationGroup.setOperationList(new ArrayList<>());
                OperationTargetHolder.getOperationGroupKit().addOperationGroup(operationGroup);
                mainModel.getOperationGroupModel().addOperationGroup(operationGroup);

                // 导入步骤组合
                Operation operation = new Operation();
                operation.setOperationMethod(CommonMethods.loadOperationGroup);
                operation.setOperationObject(groupName);
                if (moveToHead) {
                    mainModel.getOperationModel().updateOperation(operation, 0);
                } else {
                    mainModel.getOperationModel().updateOperation(operation);
                }

                // 切换到步骤组合的编辑界面
                mainModel.getOperationGroupModel().loadOperationGroup(groupName);
            }
        }
    }


    public class MoveToBackgroundTaskActionListener implements ActionListener {
        private final MainModel mainModel;

        public MoveToBackgroundTaskActionListener(MainModel mainModel) {
            this.mainModel = mainModel;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            int[] rows = defaultTable.getSelectedRows();
            for (int row : rows) {
                if (row == -1) {
                    continue;
                }
                Operation operation = defaultTable.getRow(row);
                operation.setBackground(!operation.isBackground());
                defaultTable.replaceRowData(row, operation);
                setBackground(row, operation.isBackground());
            }
            if (rows.length > 0) {
                defaultTable.save();
            }
        }
    }


    public static class LoadOperationGroupActionListener implements ActionListener {
        private final MainModel mainModel;

        public LoadOperationGroupActionListener(MainModel mainModel) {
            this.mainModel = mainModel;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            //String groupName = JOptionPane.showInputDialog("输入步骤组合名称:");
            //if (groupName != null) {
            //    Operation operation = Operation.commonOperation();
            //    operation.setOperationMethod(CommonMethods.loadOperationGroup);
            //    operation.setOperationObject(groupName);
            //    mainModel.getOperationModel().updateOperation(operation);
            //}

            List<OperationGroup> operationGroups = OperationTargetHolder.getOperationGroupKit().loadOperationGroups(mainModel.getAppInfo().getProject());

            if (!operationGroups.isEmpty()) {
                String[] strings = operationGroups.stream().map(OperationGroup::getGroupName).toArray(String[]::new);

                Object value = JOptionPane.showInputDialog(null, "请选择需要导入的动作组合",
                        "导入动作组合", JOptionPane.PLAIN_MESSAGE, null, strings, strings[0]);
                if (value != null) {
                    if (value instanceof String) {
                        String groupName = (String) value;
                        Operation operation = new Operation();
                        operation.setOperationMethod(CommonMethods.loadOperationGroup);
                        operation.setOperationObject(groupName);
                        mainModel.getOperationModel().updateOperation(operation);
                        //mainModel.getOperationGroupModel().loadOperationGroup((String) value);
                    }
                } else {
                    //System.out.println("用户取消了操作");
                }
            } else {
                JOptionPane.showMessageDialog(null, "当前无可导入的动作组合");
            }
        }
    }

    public class FailRetryActionListener extends CreateOperationGroupActionListener {
        public FailRetryActionListener(MainModel mainModel) {
            super(mainModel);
            setPresetGroupName("@预设命令#失败重试");
            setMoveToHead(true);
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            super.actionPerformed(e);
        }

    }

    /*
    * 执行当前选中行及以后得所有操作
    * */
    public class ExecuteCurrentOperationsActionListener implements ActionListener {

        private final MainModel mainModel;

        public ExecuteCurrentOperationsActionListener(MainModel mainModel) {
            this.mainModel = mainModel;
        }

        // 过滤掉循环结束 没有开始操作的数据
        private List<Operation> filterOperation(List<Operation> operationList) {
            // 收集循环开始的操作码
            List<String> pairedCodeList = operationList.stream()
                    .filter(operation -> CommonMethods.beginLoop.getKeyword().equals(operation.getOperationMethod().getKeyword()))
                    .map(operation -> operation.getOperationMethod().getPairedCode())
                    .distinct()
                    .collect(Collectors.toList());

            // 除去循环结束 没有对应的循环开始的操作码
            return operationList.stream()
                    .filter(operation -> {
                        String pairedCode = operation.getOperationMethod().getPairedCode();
                        String keyword = operation.getOperationMethod().getKeyword();
                        return !CommonMethods.endLoop.getKeyword().equals(keyword) || pairedCodeList.contains(pairedCode);
                    })
                    .collect(Collectors.toList());
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            List<Operation> operationList = defaultTable.getSelectedRowsAfterData();
            // 添加行号
            int selectedRow = defaultTable.getSelectedRow();
            for (Operation operation : operationList) {
                operation.setLineNo(selectedRow++);
            }
            // 过滤掉循环结束 没有开始操作的数据
            List<Operation> operationFilterList = filterOperation(operationList);
            if (operationFilterList.isEmpty()) {
                SwingUtil.showWarningDialog(defaultTable, "没有要执行的步骤");
            }
            OperationGroup operationGroup = new OperationGroup();
            operationGroup.setOperationList(operationFilterList);
            operationGroup.setProjectName(mainModel.getAppInfo().getProject());
            operationGroup.setGroupName(ExecutionStatus.single_test.toString()); // 单个测试 用于后端判断从当前步骤开始执行
            mainModel.getTestScriptEventModel().executeOperationGroupStarted(operationGroup);
        }
    }

    public class ExecuteOperationsActionListener implements ActionListener {

        private final MainModel mainModel;

        public ExecuteOperationsActionListener(MainModel mainModel) {
            this.mainModel = mainModel;
        }

        private boolean checkPairedMethods(List<Operation> operationList) {
            for (int row = 0; row < operationList.size(); row++) {
                Operation operation = operationList.get(row);
                OperationMethod operationMethod = operation.getOperationMethod().getPairedOperationMethod();
                if (operationMethod != null) {
                    //存在配对方法
                    boolean paired = false;
                    for (Operation searchOperation : operationList.subList(row + 1, operationList.size())) {
                        if (Objects.equals(searchOperation.getOperationMethod().getPairedCode(), operationMethod.getPairedCode())) {
                            //匹配到
                            paired = true;
                            break;
                        }
                    }
                    if (!paired) {
                        //未匹配到
                        return false;
                    }
                }
            }
            return true;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            List<Operation> operationList = defaultTable.getSelectedRowsData();
            if (!checkPairedMethods(operationList)) {
                SwingUtil.showWarningDialog(defaultTable, "存在关键字组无法正确匹配");
            } else {
                OperationGroup operationGroup = new OperationGroup();
                // 过滤掉循环开始、循环结束 没有对应配对码的数据
                List<Operation> filterList = filterOperation(operationList);
                // 添加行号
                int selectedRow = 0;
                for (Operation operation : operationList) {
                    operation.setLineNo(selectedRow++);
                }
                operationGroup.setOperationList(filterList);
                operationGroup.setProjectName(mainModel.getAppInfo().getProject());
                mainModel.getTestScriptEventModel().executeOperationGroupStarted(operationGroup);
            }
        }

        // 过滤掉循环开始、循环结束 没有对应配对码的数据
        private List<Operation> filterOperation(List<Operation> operationList) {
            // 收集循环开始的操作码
            List<String> beginPairedCodeList = operationList.stream()
                    .filter(operation -> CommonMethods.beginLoop.getKeyword().equals(operation.getOperationMethod().getKeyword()))
                    .map(operation -> operation.getOperationMethod().getPairedCode())
                    .distinct()
                    .collect(Collectors.toList());

            // 收集循环结束的操作码
            List<String> endPairedCodeList = operationList.stream()
                    .filter(operation -> CommonMethods.endLoop.getKeyword().equals(operation.getOperationMethod().getKeyword()))
                    .map(operation -> operation.getOperationMethod().getPairedCode())
                    .distinct()
                    .collect(Collectors.toList());

            // 过滤掉 循环开始、循环结束 没有对应配对码的数据
            List<Operation> filterList = new ArrayList<>();
            for (Operation operation : operationList) {
                String keyword = operation.getOperationMethod().getKeyword();
                if ((CommonMethods.beginLoop.getKeyword().equals(keyword) && !endPairedCodeList.contains(operation.getOperationMethod().getPairedCode())) ||
                        (CommonMethods.endLoop.getKeyword().equals(keyword) && !beginPairedCodeList.contains(operation.getOperationMethod().getPairedCode()))) {
                    continue;
                }
                filterList.add(operation);
            }
            return filterList;
        }
    }

    public void setAnnotation(int row, boolean annotated) {
        if (annotated) {
            defaultTable.getRowHeaderTable().setMarker(row, ANNOTATION_MARK);
        } else {
            defaultTable.getRowHeaderTable().clearMarker(row, ANNOTATION_MARK);
        }
    }

    public void setBackground(int row, boolean background) {
        if (background) {
            defaultTable.getRowHeaderTable().setMarker(row, BACKGROUND_MARK);
        } else {
            defaultTable.getRowHeaderTable().clearMarker(row, BACKGROUND_MARK);
        }
    }

    private class ModifyChannelIndexActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            SpinnerDialog<Integer> dialog = new SpinnerDialog<>("修改设备通道对话框", "通道索引:", 1);
            Integer channelIndex = dialog.getValue();
            if (channelIndex != null) {
                int[] rows = defaultTable.getSelectedRows();
                for (int row : rows) {
                    if (row == -1) {
                        continue;
                    }
                    Operation operation = defaultTable.getRow(row);
                    if (operation.getOperationTarget() != null) {
                        operation.getOperationTarget().modifyChannelIndex(channelIndex);
                        defaultTable.replaceRowData(row, operation);
                    }
                }
                if (rows.length > 0) {
                    defaultTable.save();
                }
            }
        }
    }

    private class ModifyDeviceIndexActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            SpinnerDialog<Integer> dialog = new SpinnerDialog<>("修改设备索引对话框", "设备索引:", 1);
            Integer deviceIndex = dialog.getValue();
            if (deviceIndex != null) {
                int[] rows = defaultTable.getSelectedRows();
                for (int row : rows) {
                    if (row == -1) {
                        continue;
                    }
                    Operation operation = defaultTable.getRow(row);
                    if (operation.getOperationTarget() != null) {
                        operation.getOperationTarget().modifyDeviceIndex(deviceIndex);
                        defaultTable.replaceRowData(row, operation);
                    }
                }
                if (rows.length > 0) {
                    defaultTable.save();
                }
            }
        }
    }


    private final Action undo = new AbstractAction() {
        @Override
        public void actionPerformed(ActionEvent e) {
            //System.out.println("这是撤销操作");
            TestScriptEditorTable testScriptEditorTable = (TestScriptEditorTable) defaultTable;
            TestScriptTableFileView testScriptFileView = testScriptEditorTable.getController().getPanelView().getTestScriptView().getTestScriptFileView();
            OperationCareTaker operationCareTaker = testScriptFileView.getOperationCareTaker();
            if (operationCareTaker.hasSavedStates()) {
                testScriptFileView.setUpdate(false);

                Memento memento = operationCareTaker.undo();
                testScriptFileView.restoreFromMemento(memento);
                testScriptFileView.setUpdate(true);
            } else {
                //System.out.println("没有状态可恢复了");
            }
            testScriptFileView.getOperationCareTaker().printAll();
        }
    };

    private final Action redo = new AbstractAction() {
        @Override
        public void actionPerformed(ActionEvent e) {
            //System.out.println("这是重做操作");
            TestScriptEditorTable testScriptEditorTable = (TestScriptEditorTable) defaultTable;
            TestScriptTableFileView testScriptFileView = testScriptEditorTable.getController().getPanelView().getTestScriptView().getTestScriptFileView();
            OperationCareTaker operationCareTaker = testScriptFileView.getOperationCareTaker();
            if (operationCareTaker.hasRedoStates()) {
                testScriptFileView.setUpdate(false);
                Memento memento = operationCareTaker.redo();
                testScriptFileView.restoreFromMemento(memento);
                testScriptFileView.setUpdate(true);
            } else {
                //System.out.println("没有状态可重做了");
            }
            testScriptFileView.getOperationCareTaker().printAll();
        }
    };

    private final Action annotation = new AbstractAction() {
        @Override
        public void actionPerformed(ActionEvent e) {
            int[] rows = defaultTable.getSelectedRows();
            for (int row : rows) {
                if (row == -1) {
                    continue;
                }
                Operation operation = defaultTable.getRow(row);
                operation.setAnnotated(!operation.isAnnotated());
                setAnnotation(row, operation.isAnnotated());
            }
            if (rows.length > 0) {
                defaultTable.save();
            }
        }
    };

    private class AnnotationActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            annotation.actionPerformed(e);
        }
    }


    public class AddNoteActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String note = JOptionPane.showInputDialog(null, "输入自定义注释:");
            if (note != null) {
                int[] rows = defaultTable.getSelectedRows();
                for (int row : rows) {
                    if (row == -1) {
                        continue;
                    }
                    Operation operation = defaultTable.getRow(row);
                    operation.setFriendlyOperationObject(String.format("//%s", note));
                    defaultTable.replaceRowData(row, operation);
                }
                if (rows.length > 0) {
                    defaultTable.save();
                }
            }
        }
    }

    public class IfSuccessActionListener implements ActionListener {
        private final MainModel mainModel;

        public IfSuccessActionListener(MainModel mainModel) {
            this.mainModel = mainModel;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
//            int row = defaultTable.getNextTableRow();
            Operation ifOperation = new Operation();
            ifOperation.setOperationMethod(CommonMethods.ifExpressionSuccess);
            defaultTable.addRowData(ifOperation);

            Operation elseOperation = new Operation();
            elseOperation.setOperationMethod(CommonMethods.elseExpression);
            defaultTable.addRowData(elseOperation);

            Operation endIfOperation = new Operation();
            endIfOperation.setOperationMethod(CommonMethods.endIf);
            defaultTable.addRowData(endIfOperation);
            defaultTable.save();
        }
    }

    public class CancelNoteActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int[] rows = defaultTable.getSelectedRows();
            for (int row : rows) {
                if (row == -1) {
                    continue;
                }
                Operation operation = defaultTable.getRow(row);
                operation.setFriendlyOperationObject(null);
                defaultTable.replaceRowData(row, operation);
            }
            if (rows.length > 0) {
                defaultTable.save();
            }
        }

    }


    @Override
    protected List<Operation> copy() throws IOException, ClassNotFoundException {
        List<Operation> copyList = CopyUtils.deepCopyList(defaultTable.getSelectedRowsData());
        for (Operation operation : copyList) {
            operation.reGenerateUUID();
        }
        return copyList;
    }

    public class WrappedInnerLoopActionListener implements ActionListener {
        private final MainModel mainModel;

        public WrappedInnerLoopActionListener(MainModel mainModel){
            this.mainModel = mainModel;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            // 获取选中行
            int[] selectedRows = defaultTable.getSelectedRows();
            int upperIndex = selectedRows[0];
            int bottomIndex = selectedRows[selectedRows.length - 1];
            // 插入循环标记
            AssistantActionHelper assistantActionHelper;
            assistantActionHelper = new AssistantActionHelper(mainModel);
            assistantActionHelper.wrappedInnerLoop(upperIndex, bottomIndex+2);
        }
    }

    public class LabelConfigActionListener implements ActionListener {
        private final MainModel mainModel;

        public LabelConfigActionListener(MainModel mainModel){
            this.mainModel = mainModel;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            // 获取选中行
            int[] selectedRows = defaultTable.getSelectedRows();

            LabelConfigDialog dialog = new LabelConfigDialog(mainModel);
            dialog.setVisible(true);

//            if (dialog.getResult() != null) {
//                System.out.println("最终标签配置:");
//                dialog.getResult().forEach(operationLabel ->
//                        System.out.println(operationLabel.name + " - " + operationLabel.color)
//                );
//            }
        }
    }

    public class SetLabelActionListener implements ActionListener {
        private final MainModel mainModel;
        private final OperationLabel operationLabel;


        public SetLabelActionListener(MainModel mainModel, OperationLabel operationLabel){
            this.mainModel = mainModel;
            this.operationLabel = operationLabel;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            int[] rows = defaultTable.getSelectedRows();
            for (int row : rows) {
                if (row == -1) {
                    continue;
                }
                Operation operation = defaultTable.getRow(row);
                operation.setLabel(operationLabel);
//                mainModel.getOperationModel().updateOperation(operation,false);
            }
            if (rows.length > 0) {
                // 在调用 save() 前打印类型
                defaultTable.save();
            }
        }
    }

    public class CancelLabelActionListener implements ActionListener {
        private final MainModel mainModel;

        public CancelLabelActionListener(MainModel mainModel){
            this.mainModel = mainModel;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            int[] rows = defaultTable.getSelectedRows();
            // 批量取消标签
            for(int row : rows){
                defaultTable.getSideBarTable().cancelOperationLabelForRow(row);
            }
            if (rows.length > 0) {
                // 在调用 save() 前打印类型
                defaultTable.save();
            }
        }
    }
//    @Getter
//    private final Action pasteAction = new AbstractAction() {
//        @Override
//        public void actionPerformed(ActionEvent e) {
//            List<Operation> clipBoard = getClipBoard();
//            log.info("粘贴的内容：{}", clipBoard);
//            TestScriptEditorTable testScriptEditorTable = (TestScriptEditorTable) defaultTable;
//            testScriptEditorTable.getMainModel().getClipBoardModel().getPasteFocus();
//            //defaultTable.pasteRowsActivated(clipBoard);
//            //defaultTable.save();
//        }
//    };

}
