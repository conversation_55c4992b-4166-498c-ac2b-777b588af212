package ui.layout.left.display.dialogs;

import org.jetbrains.annotations.NotNull;
import sdk.domain.SmokingTestConfigModel;
import sdk.entity.OperationTargetHolder;
import ui.base.BaseView;
import ui.config.json.SmokingTestJsonConfig;
import ui.config.xml.app.AppConfiguration;
import ui.entry.ClientView;
import ui.model.MainModel;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.EventObject;
import java.util.Objects;

import static sdk.domain.SmokingTestConfigModel.getFileType;

public class SmokingTestMgmtDialog extends ConfirmDialog implements BaseView {
    private final MainModel mainModel;
    private final JCheckBox isListeningUpgradeNotificationsCheckBox;
    private final JCheckBox isEnableSequenceEmailSendingCheckBox;
    private final JCheckBox isEnableSequenceRobotSendingCheckBox;
    private final JCheckBox isEnableSequenceCloudDocSendingCheckBox;
    private final JComboBox<String> fileTypeComboBox;
    private final JComboBox<String> testModeComboBox;
    private final JTextField textField;
    private final JButton button;
    private final ClientView clientView;
    private final JTextField versionTextField;
    private final JTextField reportUrlTextField;
    private final JTextField passRateTextField;
    private final JCheckBox isModulePassRateCheckBox;
    private final DefaultTableModel emailTableModel;
    private final DefaultTableModel urlsTableModel;
    private final DefaultTableModel multiTableUrlsTableModel;

    public SmokingTestMgmtDialog(MainModel mainModel, ClientView clientView) {
        this.mainModel = mainModel;
        this.clientView = clientView;
        setTitle("冒烟配置");
        isListeningUpgradeNotificationsCheckBox = new JCheckBox();
        isEnableSequenceEmailSendingCheckBox = new JCheckBox();
        isEnableSequenceRobotSendingCheckBox = new JCheckBox();
        isEnableSequenceCloudDocSendingCheckBox = new JCheckBox();
        fileTypeComboBox = new JComboBox<>();
        fileTypeComboBox.setPreferredSize(new Dimension(130, 30));
        fileTypeComboBox.addItem(".s19");
        fileTypeComboBox.addItem(".hex");
        fileTypeComboBox.addItem(".bin");
        testModeComboBox = new JComboBox<>();
        testModeComboBox.setPreferredSize(new Dimension(130, 30));
        testModeComboBox.addItem("功能测试");
        testModeComboBox.addItem("冒烟点检");
        textField = new JTextField(20);
        button = new JButton("选择");
        button.addActionListener(e -> {
            JFileChooser fileChooser = new JFileChooser();
            fileChooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
            int result = fileChooser.showOpenDialog(null);
            if (result == JFileChooser.APPROVE_OPTION) {
                File selectedFile = fileChooser.getSelectedFile();
                textField.setText(selectedFile.getAbsolutePath());
            }
        });
        versionTextField = new JTextField(10);
        reportUrlTextField = new JTextField(20);
        passRateTextField = new JTextField(5);
        isModulePassRateCheckBox = new JCheckBox("所有模块分别通过率达到目标值才能通过");
        isModulePassRateCheckBox.setSelected(true);
        emailTableModel = new DefaultTableModel(new Object[]{"Email地址"}, 0);
        urlsTableModel = new DefaultTableModel(new Object[]{"飞书机器人地址"}, 0);
        multiTableUrlsTableModel = new DefaultTableModel(new Object[]{"多维表格地址"}, 0);
        createView();
        createActions();
        restoreView();
        // 添加监听逻辑
        isEnableSequenceCloudDocSendingCheckBox.addActionListener(e -> {
            if (isEnableSequenceCloudDocSendingCheckBox.isSelected()) {
                if (multiTableUrlsTableModel.getRowCount() == 0) {
                    JOptionPane.showMessageDialog(this,
                            "请先添加待写入的多维表格URL!",
                            "提示",
                            JOptionPane.WARNING_MESSAGE);
                    // 取消选中
                    isEnableSequenceCloudDocSendingCheckBox.setSelected(false);
                }
            }
        });
    }

    @Override
    public void createView() {
        super.createView();
        setSize(500, 400);
        setLocationRelativeTo(null);
        //SwingUtil.centerInScreen(this, false);
        testModeComboBox.setSelectedIndex(mainModel.getAppInfo().isSmokeTest() ? 1 : 0);
        isEnableSequenceEmailSendingCheckBox.setSelected(mainModel.getAppInfo().isEnableSequenceEmailSending());
        isEnableSequenceRobotSendingCheckBox.setSelected(mainModel.getAppInfo().isEnableSequenceRobotSending());
        isEnableSequenceCloudDocSendingCheckBox.setSelected(mainModel.getAppInfo().isEnableSequenceCloudDocSending());
        SmokingTestConfigModel smokingTestConfig = SmokingTestJsonConfig.getInstance().getSmokingTestConfig();
        if (smokingTestConfig != null) {
            isListeningUpgradeNotificationsCheckBox.setSelected(smokingTestConfig.isListeningUpgradeNotification());
            SmokingTestConfigModel.UpgradeFileType fileType = smokingTestConfig.getFileType();
            fileTypeComboBox.setSelectedItem(fileType == null ? 0 : fileType.getValue());
            textField.setText(smokingTestConfig.getSmokingTestReportPath());
            versionTextField.setText(smokingTestConfig.getVersion());
            reportUrlTextField.setText(smokingTestConfig.getReportUrl());
            passRateTextField.setText(smokingTestConfig.getReportPassRate());
            isModulePassRateCheckBox.setSelected(smokingTestConfig.isModulePassRateRequired());
        }

    }

    @Override
    public JPanel makeCenterPanel() {
        JTabbedPane tabbedPane = new JTabbedPane();

        JPanel smokePanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        smokePanel.add(createPane(new JLabel("是否持续监听升级通知:"), isListeningUpgradeNotificationsCheckBox));
        smokePanel.add(createPane(new JLabel("测试模式:"), testModeComboBox));
        smokePanel.add(createPane(new JLabel("升级文件类型:"), fileTypeComboBox));
//        panel.add(createPane(new JLabel("冒烟报告路径:"), textField, button));   //已经自动生成冒烟测试
        smokePanel.add(createPane(new JLabel("版本号:"), versionTextField));
        smokePanel.add(createPane(new JLabel("报告网盘地址:"), reportUrlTextField));
        smokePanel.add(createPane(isModulePassRateCheckBox));
        smokePanel.add(createPane(new JLabel("Pass通过率 (%):"), passRateTextField));// Add Pass Rate to the panel
        smokePanel.add(createPane(new JLabel("是否启用邮件通知:"), isEnableSequenceEmailSendingCheckBox));
        smokePanel.add(createPane(new JLabel("是否启用飞书机器人通知:"), isEnableSequenceRobotSendingCheckBox));
        smokePanel.add(createPane(new JLabel("是否启用云文档通知:"), isEnableSequenceCloudDocSendingCheckBox));
        tabbedPane.addTab("冒烟配置", smokePanel);
        // Email设置选项卡
        JPanel emailSettingsPanel = creatEmailPanel();
        tabbedPane.addTab("邮件通知设置", emailSettingsPanel);
        JPanel robotSettingsPanel = createRobotPanel();
        tabbedPane.addTab("飞书机器人设置", robotSettingsPanel);
        JPanel multiTableSettingsPanel = createMultiTableUrlsPanel();
        tabbedPane.addTab("多维表格设置", multiTableSettingsPanel);
        JPanel panel = new JPanel(new BorderLayout());
        panel.add(tabbedPane, BorderLayout.CENTER);
        return panel;
    }

    @NotNull
    private JPanel creatEmailPanel() {
        JPanel emailSettingsPanel = new JPanel(new BorderLayout());
        JTable emailTable = new JTable(emailTableModel) {
            @Override
            public boolean editCellAt(int row, int column, EventObject e) {
                // 如果是鼠标事件并且点击次数大于1（即双击），则不允许编辑
                if (e instanceof MouseEvent) {
                    MouseEvent me = (MouseEvent) e;
                    if (me.getClickCount() > 1) {
                        return false;
                    }
                }
                return super.editCellAt(row, column, e);
            }
        };
        JScrollPane scrollPane = new JScrollPane(emailTable);
        emailSettingsPanel.add(scrollPane, BorderLayout.CENTER);

        JButton addButton = new JButton("添加");
        addButton.addActionListener(e -> {
            String newEmail;
            boolean isValidEmail;

            do {
                newEmail = JOptionPane.showInputDialog(SmokingTestMgmtDialog.this, "输入新的Email地址:");
                isValidEmail = validateEmail(newEmail);

                if (newEmail == null || newEmail.trim().isEmpty()) {
                    return;
                }

                if (!isValidEmail) {
                    JOptionPane.showMessageDialog(SmokingTestMgmtDialog.this, "请输入有效的Email地址。", "无效输入", JOptionPane.ERROR_MESSAGE);
                }
            } while (!isValidEmail);

            emailTableModel.addRow(new Object[]{newEmail});
        });

        JButton deleteButton = new JButton("删除");
        deleteButton.setEnabled(false);
        deleteButton.addActionListener(e -> {
            int selectedRow = emailTable.getSelectedRow();
            if (selectedRow != -1) {
                emailTableModel.removeRow(selectedRow);
                deleteButton.setEnabled(false); // 删除后禁用删除按钮
            }
        });

        emailTable.getSelectionModel().addListSelectionListener(e -> deleteButton.setEnabled(emailTable.getSelectedRow() != -1));

        JPanel buttonPanel = new JPanel();

        buttonPanel.add(addButton);
        buttonPanel.add(deleteButton);
        emailSettingsPanel.add(buttonPanel, BorderLayout.SOUTH);
        return emailSettingsPanel;
    }

    @NotNull
    private JPanel createRobotPanel() {
        JPanel robotSettingsPanel = new JPanel(new BorderLayout());
        JTable robotTable = new JTable(urlsTableModel) {
            @Override
            public boolean editCellAt(int row, int column, EventObject e) {
                // 如果是鼠标事件并且点击次数大于1（即双击），则不允许编辑
                if (e instanceof MouseEvent) {
                    MouseEvent me = (MouseEvent) e;
                    if (me.getClickCount() > 1) {
                        return false;
                    }
                }
                return super.editCellAt(row, column, e);
            }
        };
        JScrollPane scrollPane = new JScrollPane(robotTable);
        robotSettingsPanel.add(scrollPane, BorderLayout.CENTER);

        JButton addButton = new JButton("添加");
        addButton.addActionListener(e -> {
            String newUrl;
            boolean isValidUrl;

            do {
                newUrl = JOptionPane.showInputDialog(SmokingTestMgmtDialog.this, "输入新的Robot URL:");
                if (newUrl == null || newUrl.trim().isEmpty()) {
                    return;
                }
                isValidUrl = validateURL(newUrl);

                if (!isValidUrl) {
                    JOptionPane.showMessageDialog(SmokingTestMgmtDialog.this, "请输入有效的URL。", "无效输入", JOptionPane.ERROR_MESSAGE);
                }
            } while (!isValidUrl);

            urlsTableModel.addRow(new Object[]{newUrl});
        });

        JButton deleteButton = new JButton("删除");
        deleteButton.setEnabled(false);
        deleteButton.addActionListener(e -> {
            int selectedRow = robotTable.getSelectedRow();
            if (selectedRow != -1) {
                urlsTableModel.removeRow(selectedRow);
                deleteButton.setEnabled(false); // 删除后禁用删除按钮
            }
        });

        robotTable.getSelectionModel().addListSelectionListener(e -> deleteButton.setEnabled(robotTable.getSelectedRow() != -1));

        JPanel buttonPanel = new JPanel();
        buttonPanel.add(addButton);
        buttonPanel.add(deleteButton);
        robotSettingsPanel.add(buttonPanel, BorderLayout.SOUTH);
        return robotSettingsPanel;
    }

    @NotNull
    private JPanel createMultiTableUrlsPanel() {
        JPanel multiTableUrlsSettingsPanel = new JPanel(new BorderLayout());
        JTable multiTableUrlsTable = new JTable(multiTableUrlsTableModel) {
            @Override
            public boolean editCellAt(int row, int column, EventObject e) {
                // 如果是鼠标事件并且点击次数大于1（即双击），则不允许编辑
                if (e instanceof MouseEvent) {
                    MouseEvent me = (MouseEvent) e;
                    if (me.getClickCount() > 1) {
                        return false;
                    }
                }
                return super.editCellAt(row, column, e);
            }
        };
        JScrollPane scrollPane = new JScrollPane(multiTableUrlsTable);
        multiTableUrlsSettingsPanel.add(scrollPane, BorderLayout.CENTER);

        JButton addButton = new JButton("添加");
        addButton.addActionListener(e -> {
            String newMultiTableUrl;
            boolean isValidMultiTableUrl;

            do {
                newMultiTableUrl = JOptionPane.showInputDialog(SmokingTestMgmtDialog.this, "输入新的多维表格URL:");
                if (newMultiTableUrl == null || newMultiTableUrl.trim().isEmpty()) {
                    return;
                }
                isValidMultiTableUrl = validateURL(newMultiTableUrl);

                if (!isValidMultiTableUrl) {
                    JOptionPane.showMessageDialog(SmokingTestMgmtDialog.this, "请输入有效的URL。", "无效输入", JOptionPane.ERROR_MESSAGE);
                }
            } while (!isValidMultiTableUrl);

            multiTableUrlsTableModel.addRow(new Object[]{newMultiTableUrl});
        });

        JButton deleteButton = new JButton("删除");
        deleteButton.setEnabled(false);
        deleteButton.addActionListener(e -> {
            int selectedRow = multiTableUrlsTable.getSelectedRow();
            if (selectedRow != -1) {
                multiTableUrlsTableModel.removeRow(selectedRow);
                deleteButton.setEnabled(false); // 删除后禁用删除按钮
            }
        });

        multiTableUrlsTable.getSelectionModel().addListSelectionListener(e -> deleteButton.setEnabled(multiTableUrlsTable.getSelectedRow() != -1));

        JPanel buttonPanel = new JPanel();
        buttonPanel.add(addButton);
        buttonPanel.add(deleteButton);
        multiTableUrlsSettingsPanel.add(buttonPanel, BorderLayout.SOUTH);
        return multiTableUrlsSettingsPanel;
    }

    @Override
    public void setVisible(boolean b) {
        if (b) {
            // 清空现有的表格内容
            emailTableModel.setRowCount(0);
            urlsTableModel.setRowCount(0);
            multiTableUrlsTableModel.setRowCount(0);

            // 加载 Email 列表到表格中
            String[] emailList = mainModel.getAppInfo().getEmails();
            if (emailList != null) {
                for (String email : emailList) {
                    emailTableModel.addRow(new Object[]{email});
                }
            }

            // 加载 URLs 列表到表格中
            String[] urlList = mainModel.getAppInfo().getUrls();
            if (urlList != null) {
                for (String url : urlList) {
                    urlsTableModel.addRow(new Object[]{url});
                }
            }

            //加载 多维表格urlS 到表格中
            String[] multiTableUrlList = mainModel.getAppInfo().getMultiTableUrls();
            if (multiTableUrlList != null) {
                for (String multiTableUrl : multiTableUrlList) {
                    multiTableUrlsTableModel.addRow(new Object[]{multiTableUrl});
                }
            }
        }
        super.setVisible(b);
    }

    @Override
    protected boolean performConfirm() {
        if (!validatePassRate()) {
            return false;
        }
        SmokingTestConfigModel smokingTestConfigModel = new SmokingTestConfigModel();
        smokingTestConfigModel.setListeningUpgradeNotification(isListeningUpgradeNotificationsCheckBox.isSelected());
        smokingTestConfigModel.setFileType(getFileType(Objects.requireNonNull(fileTypeComboBox.getSelectedItem()).toString()));
        smokingTestConfigModel.setSmokingTestReportPath(textField.getText());
        smokingTestConfigModel.setVersion(versionTextField.getText());
        smokingTestConfigModel.setReportUrl(reportUrlTextField.getText());
        smokingTestConfigModel.setReportPassRate(passRateTextField.getText());
        smokingTestConfigModel.setModulePassRateRequired(isModulePassRateCheckBox.isSelected()); // 保存状态

        // 保存 Emails
        int emailRowCount = emailTableModel.getRowCount();
        String[] emailList = new String[emailRowCount];
        for (int i = 0; i < emailRowCount; i++) {
            emailList[i] = (String) emailTableModel.getValueAt(i, 0);
        }
        mainModel.getAppInfo().setEmails(emailList);

        // 保存 URLs
        int urlRowCount = urlsTableModel.getRowCount();
        String[] urlList = new String[urlRowCount];
        for (int i = 0; i < urlRowCount; i++) {
            urlList[i] = (String) urlsTableModel.getValueAt(i, 0);
        }
        mainModel.getAppInfo().setUrls(urlList);

        //保存多维表格urls
        int multiTableUrlRowCount = multiTableUrlsTableModel.getRowCount();
        String[] multiTableUrlList = new String[multiTableUrlRowCount];
        for (int i = 0; i < multiTableUrlRowCount; i++) {
            multiTableUrlList[i] = (String) multiTableUrlsTableModel.getValueAt(i, 0);
        }
        mainModel.getAppInfo().setMultiTableUrls(multiTableUrlList);

        mainModel.getAppInfo().setSmokeTest(testModeComboBox.getSelectedIndex() == 1);
        mainModel.getAppInfo().setEnableSequenceEmailSending(isEnableSequenceEmailSendingCheckBox.isSelected());
        mainModel.getAppInfo().setEnableSequenceRobotSending(isEnableSequenceRobotSendingCheckBox.isSelected());
        mainModel.getAppInfo().setEnableSequenceCloudDocSending(isEnableSequenceCloudDocSendingCheckBox.isSelected());

        //保存去appInfo
        OperationTargetHolder.getTestClientKit().configurationEmails(mainModel.getAppInfo());
        OperationTargetHolder.getTestClientKit().configurationRobotUrls(mainModel.getAppInfo());

        OperationTargetHolder.getSmokingTestKit().smokingTestConfig(smokingTestConfigModel);
        SmokingTestJsonConfig.getInstance().setSmokingTestConfigModel(smokingTestConfigModel);
        clientView.setSmokingTestIcon();
        SmokingTestJsonConfig.getInstance().save();

        AppConfiguration.getInstance().getAppConfig().setAppInfo(mainModel.getAppInfo()).save();

        return super.performConfirm();
    }

    private JComponent createPane(JComponent... components) {
        JPanel panel = new JPanel();
        for (JComponent component : components) {
            panel.add(component);
        }
        return panel;
    }

    private boolean validatePassRate() {
        try {
            double passRate = Double.parseDouble(passRateTextField.getText());
            if (passRate < 0 || passRate > 100) {
                JOptionPane.showMessageDialog(this, "Pass通过率必须是0到100之间的数字。", "无效输入", JOptionPane.ERROR_MESSAGE);
                return false;
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "请输入有效的Pass通过率。", "无效输入", JOptionPane.ERROR_MESSAGE);
            return false;
        }
        return true;
    }

    private boolean validateEmail(String email) {
        if (email == null) {
            return false;
        }

        String emailRegex = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
        return email.matches(emailRegex);
    }

    /**
     * 验证 URL 的格式
     */
    private boolean validateURL(String url) {
        try {
            new URL(url);
            return true;
        } catch (MalformedURLException e) {
            return false;
        }
    }
}
