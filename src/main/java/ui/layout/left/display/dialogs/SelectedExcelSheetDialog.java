package ui.layout.left.display.dialogs;

import excelcase.tree.SelectedExcelSheetTree;
import ui.base.BaseView;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/3/27 11:43
 * @description : 要选择测试表的对话框
 * @modified By :
 * @since : 2023/3/27
 **/
public class SelectedExcelSheetDialog extends JDialog implements BaseView {
    private final SelectedExcelSheetTree selectedExcelSheetTree;
    private final Container container;
    private final JButton confirmBtn;
    private DialogCallback callback;
    // 添加加载指示器
    private final JLabel loadingLabel;
    private JPanel bottomPanel;

    public SelectedExcelSheetDialog(MainModel mainModel, Map<String, Boolean> selectionSheets) {
        selectedExcelSheetTree = new SelectedExcelSheetTree(selectionSheets);
        container = this.getContentPane();
        confirmBtn = new JButton("确定");
        // 初始化加载指示器
        loadingLabel = new JLabel("处理中...");
        loadingLabel.setVisible(false);
    }

    public void showDialog() {
        createView();
        createActions();
        finalSetting();
    }

    @Override
    public void createView() {
        setTitle("选择加载表");
        container.setSize(new Dimension(800, 50));
        container.setLayout(new BorderLayout());
        JScrollPane scroll = new JScrollPane(selectedExcelSheetTree);
        container.add(scroll, BorderLayout.CENTER);

        // 使用面板来容纳按钮和加载指示器
        bottomPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        bottomPanel.add(confirmBtn);
        bottomPanel.add(loadingLabel);
        container.add(bottomPanel, BorderLayout.SOUTH);
    }

    @Override
    public void createActions() {
        confirmBtn.addActionListener(e -> saveSelectedExcelSheetConfig());
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                if (callback != null) {
                    callback.onDialogClosed();
                }
                closeDialog();
            }
        });
    }


    private void saveSelectedExcelSheetConfig() {
        // 禁用确认按钮
        confirmBtn.setEnabled(false);
        // 显示加载指示器
        loadingLabel.setVisible(true);

        new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() throws Exception {
                LinkedHashMap<String, Boolean> selectedTreeNode = (LinkedHashMap<String, Boolean>) selectedExcelSheetTree.getSelectedTreeNode();
                if (callback != null) {
                    callback.onDialogSaved(selectedTreeNode);
                }
                return null;
            }

            @Override
            protected void done() {
                // 恢复按钮状态和隐藏加载指示器（虽然会关闭对话框，但这是个好习惯）
                confirmBtn.setEnabled(true);
                loadingLabel.setVisible(false);
                closeDialog();
            }
        }.execute();
    }

    private void finalSetting() {
        SwingUtil.centerInScreen(this);
        setModal(true);
        setVisible(true);
        setResizable(false);
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
    }

    public void addCallback(DialogCallback callback) {
        this.callback = callback;
    }

    /**
     * 关闭对话框
     */
    private void closeDialog() {
        setModal(false);
        dispose();
    }

}
