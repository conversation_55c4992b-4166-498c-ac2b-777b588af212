package ui.layout.left.display.components.treemenu.devicetree.manager;

import common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationResult;
import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.tappane.base.DeviceTabPaneView;
import ui.layout.right.components.log.LogCmd;
import ui.layout.right.components.log.LogMessage;
import ui.model.MainModel;
import ui.utils.SwingUtil;

/**
 * 设备状态管理器
 **/
@Slf4j
public class DeviceStatusManager {
    private final MainModel mainModel;
    private final ClientView clientView;
    private volatile static DeviceStatusManager deviceStatusManager;

    public DeviceStatusManager(MainModel mainModel, ClientView clientView) {
        this.mainModel = mainModel;
        this.clientView = clientView;
    }

    public static DeviceStatusManager getInstance(MainModel model, ClientView clientView) {
        if (deviceStatusManager == null) {
            synchronized (DeviceStatusManager.class) {
                if (deviceStatusManager == null) {
                    deviceStatusManager = new DeviceStatusManager(model, clientView);
                }
            }
        }
        return deviceStatusManager;
    }

    /**
     * 检查设备是否连接
     *
     * @param device 设备
     * @return 设备是否连接
     */
    private synchronized boolean checkDeviceConnected(Device device) {
        boolean connected = device.isConnected();
        DeviceTabPaneView paneView = clientView.getLeftPanelController().getLeftPanelView().getDisplayTabPane().getTabPaneViewByDeviceTypeName(device.getDeviceTypeName());
        if (paneView != null) {
            int indexOfTab = paneView.indexOfTab(device.getFriendlyName());
            return connected && (indexOfTab != -1);
        }
        return false;
    }

    /**
     * 打开设备
     *
     * @param device 设备
     */
    private void openDevice(Device device, boolean autoOpenChannel) {
        OperationResult openDeviceRes = autoOpenChannel ? device.autoOpenDevice() : device.openDevice();
        if (openDeviceRes.isOk()) {
            device.setConnected(true);
            log.info("设备连接成功:{}", device);
            LogCmd.getInstance().printLog(LogMessage.info(String.format("设备连接成功：%s-%s", device.getDeviceModel(), device.getDeviceName())));
            mainModel.getDeviceManageModel().deviceConnected(device, autoOpenChannel);
        } else {
            device.setConnected(false);
            log.info("设备连接失败:{}", device);
            LogCmd.getInstance().printLog(LogMessage.error(String.format("设备连接失败：%s-%s", device.getDeviceModel(), device.getDeviceName())));
            SwingUtil.showWarningDialog(openDeviceRes.getMessage() == null ? "设备连接失败" : openDeviceRes.getMessage());
        }
    }


    /**
     * 连接设备
     *
     * @param device          设备
     * @param tryConnect      是否尝试去连接
     * @param autoOpenChannel 是否自动打开通道
     * @return 设备
     */
    public Device connect(Device device, boolean tryConnect, boolean autoOpenChannel) {
        if (checkDeviceConnected(device)) {
            log.info("设备已经连接:{}", device);
            return device;
        }
        log.info("注册设备:{}", StringUtils.toPrettyJsonString(device));
        device.getDeviceOperationParameter().put("project", mainModel.getAppInfo().getProject());
        JsonResponse<Device> deviceRegisterRes = device.registerDevice(device);
        if (deviceRegisterRes.isOk()) {
            Device registeredDevice = deviceRegisterRes.getData();
            Device.copyDeviceProperties(registeredDevice, device);
            if (tryConnect) {
                openDevice(device, autoOpenChannel);
            }
        } else {
            log.warn("设备连接异常:{}", deviceRegisterRes.getMessage());
            LogCmd.getInstance().printLog(LogMessage.error(String.format("设备连接异常：%s", deviceRegisterRes.getMessage())));
        }
        return device;
    }

    /**
     * 断开设备
     *
     * @param device 设备
     */
    public void disconnect(Device device) {
        if (device != null) {
            JsonResponse<String> response = device.disconnectDevice();
            String message;
            if (response.isOk()) {
                message = String.format("设备%s断开成功：%s", device.getDeviceName(), response.getMessage());
            } else {
                message = String.format("设备%s断开失败：%s", device.getDeviceName(), response.getMessage());
                SwingUtil.showWarningDialog(null, message);
            }
            log.info(message);
            LogCmd.getInstance().printLog(LogMessage.error(message));
            device.setConnected(false);
            mainModel.getDeviceManageModel().deviceDisconnected(device);
        }
    }

    /**
     * 移除设备
     *
     * @param device 设备
     */
    public void remove(Device device) {
        mainModel.getDeviceManageModel().deviceRemoved(device);
    }

}
