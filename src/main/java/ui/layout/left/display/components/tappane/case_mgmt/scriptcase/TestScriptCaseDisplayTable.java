package ui.layout.left.display.components.tappane.case_mgmt.scriptcase;

import com.alibaba.fastjson2.JSON;
import common.constant.AppConstants;
import common.utils.FileUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import sdk.base.JsonResponse;
import sdk.base.execution.*;
import sdk.base.operation.Operation;
import sdk.base.operation.TestResultReport;
import sdk.domain.ScriptSortFile;
import sdk.domain.TestScriptFile;
import sdk.domain.TestScriptFileContent;
import sdk.domain.TestScriptFileSelector;
import sdk.entity.OperationTargetHolder;
import ui.base.BaseView;
import ui.base.cosntants.ExcelConstants;
import ui.base.dialogs.SpinnerDialog;
import ui.base.renderer.CheckBoxCellRenderer;
import ui.base.renderer.ColorTableRenderer;
import ui.base.table.*;
import ui.base.table.checkbox.CheckboxTable;
import ui.callback.TableMenuCallback;
import ui.config.json.ScriptSortConfig;
import ui.config.xml.project.ProjectConfiguration;
import ui.model.MainModel;
import ui.model.app.AppObserver;
import ui.model.testScript.ClipBoardObserver;
import ui.model.testScript.TestScriptEventObserver;
import ui.model.test_executor.TestExecuteStatusObserver;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 自动化脚本展示JTable
 */
@Slf4j
public class TestScriptCaseDisplayTable extends CheckboxTable<TestScriptFile> implements BaseView, AppObserver, TestScriptEventObserver, TestExecuteStatusObserver, TableMenuCallback<TestScriptFile>, TableRowTransferHandler.DragRowsEventListener, ClipBoardObserver {
    private static final TableCell<String> testScriptCaseId = new TableCell<>(1, "脚本id");
    private static final TableCell<String> testScriptModuleName = new TableCell<>(2, "脚本模块");
    private static final TableCell<String> testScriptCaseName = new TableCell<>(3, "脚本名称");
    private static final TableCell<String> testScriptCaseComment = new TableCell<>(4, "脚本备注");
    private static final TableCell<String> testScriptResult = new TableCell<>(5, "失败率");
    @Getter
    private final MainModel mainModel;
    private final ProjectConfiguration projectConfiguration;
    private TableRowTransferHandler tableRowTransferHandler;
    public static boolean scriptEmpty = true;
    private int lastSelectedRow = -1; // 添加成员变量记录上次选中的行

    @Override
    protected String[] getColumns() {
        return new String[]{checkBox.getColumnName(), testScriptCaseId.getColumnName(), testScriptModuleName.getColumnName(), testScriptCaseName.getColumnName(), testScriptCaseComment.getColumnName(), testScriptResult.getColumnName()};
    }

    public TestScriptCaseDisplayTable(MainModel mainModel) {
        super();
        this.mainModel = mainModel;
        //new
        //this.getTableActionManager().setMainModel(mainModel);

        projectConfiguration = ProjectConfiguration.getInstance(mainModel.getAppInfo().getProject());
        createView();
        createActions();
        registerModelObservers();

        initDragRows();
        save();
    }

    private void initDragRows() {
        new Thread(() -> {
            tableRowTransferHandler = new TableRowTransferHandler(this);
            tableRowTransferHandler.addDragRowsEventListener(this);
            setDragEnabled(true);
            setDropMode(DropMode.INSERT_ROWS);
            setTransferHandler(tableRowTransferHandler);
        }).start();
    }

    public void clearResults() {
        for (int row = 0; row < getRowCount(); row++) {
            setValueAt("", row, testScriptResult.getColumnIndex());
        }
    }

    @Override
    protected Object[] convertData(TestScriptFile testScriptFile) {
        return new Object[]{testScriptFile.isSelected(), testScriptFile.getId(), testScriptFile.getModuleName(), testScriptFile.getCaseName(), testScriptFile.getComment()};
    }

    @Override
    public void registerModelObservers() {
        mainModel.getAppModel().registerObserver(this);
        mainModel.getTestScriptEventModel().registerObserver(this);
        mainModel.getTestExecuteStatusModel().registerObserver(this);
        mainModel.getClipBoardModel().registerObserver(this);
    }

    @Override
    public void restoreView() {
        // 恢复上次选中的脚本
        Long testScriptId = projectConfiguration.getCaseConfig().getTestScriptId();
        if (testScriptId != null) {
            List<TestScriptFile> testScriptFiles = getTableList();
            Optional<TestScriptFile> optional = testScriptFiles.stream().filter(f -> f.getId().equals(testScriptId)).findFirst();
            clearSelection();
            if (optional.isPresent()) {
                selectRow(testScriptFiles.indexOf(optional.get()));
            } else {
                selectLastRow();
            }
        }
    }

    @Override
    public void uiStable() {
        restoreView();
    }

    @Override
    protected void createMenu() {
        super.createMenu();
        addExecuteCaseMenu();
        addModifyTestCycleMenu();
        addMenuSeparator();
        addDelMenu();
        addClearMenu();
        addMenuSeparator();
        addMoreMenu();
        //new
        addMenuSeparator();
        addCopyMenu();
        addPasteMenu();
        addDownCopyMenu();
    }

    @Override
    protected TableActionManager<TestScriptFile> initTableActionManager() {
        //return new TableActionManager<>(this);
        return new TestScriptCaseDisplayActionManager(this);
    }


    /**
     * 重写粘贴逻辑，粘贴复制文件
     *
     * @param clipBoard
     */
    @Override
    public void pasteRowsActivated(List<TestScriptFile> clipBoard) {
        for (TestScriptFile source : clipBoard) {
            // 将源文件的配置信息复制到目的文件
            TestScriptFile request = new TestScriptFile();
            request.setProjectName(source.getProjectName());
            request.setClientName(source.getClientName());
            //target.setCaseName(source.getCaseName() + " " + UUID.randomUUID());
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
            LocalDateTime now = LocalDateTime.now();
            request.setCaseName(source.getCaseName() + " " + dateTimeFormatter.format(now));


            // 请求后端，创建脚本文件
            JsonResponse<TestScriptFile> resp = OperationTargetHolder.getTestScriptKit().addTestScriptFile(request);
            log.info(JSON.toJSONString(resp));
            if (resp.isOk()) {
                TestScriptFile file = resp.getData();
                //mainModel.getTestScriptEventModel().newScript(file);

                // 复制内容到新创建的脚本内容
                TestScriptFileContent testScriptFileContent = new TestScriptFileContent();
                //testScriptFileContent.setId(file.getId());
                testScriptFileContent.setUuid(file.getUuid());

                //请求后端获取源脚本的内容
                JsonResponse<TestScriptFileContent> testScriptFileContentJsonResponse = OperationTargetHolder.getTestScriptKit().loadTestScriptFileContent(source.getUuid());
                TestScriptFileContent data = testScriptFileContentJsonResponse.getData();
                if (data != null) {
                    testScriptFileContent.setTestCycle(data.getTestCycle());
                    testScriptFileContent.setOperationList(data.getOperationList());
                }

                OperationTargetHolder.getTestScriptKit().updateTestScriptContent(testScriptFileContent);
                addRowData(file);
                //table.save();
                //testScriptCaseDisplayTable.addRowData(file);
                //testScriptCaseDisplayTable.save();
            } else {
                log.error("请求添加脚本失败");
            }
        }
    }


    private void addExecuteCaseMenu() {
        makePopupMenu("执行当前用例", null, new addExecuteCaseActionListener());
    }

    private void addModifyTestCycleMenu() {
        makePopupMenu("修改执行次数", null, new addModifyTestCycleListener());
    }

    private void addMoreMenu() {
        makePopupMenu("打开脚本文件夹", null, new AddOpenFolderActionListener());
        makePopupMenu("打开原始脚本", null, new AddOpenOrginalFileActionListener());
    }

    @Override
    public void dragRowsCompleted(Map<Integer, Integer> map) {
        swapTableList(map);
        save();
    }


    @Override
    protected void save() {
        ScriptSortFile scriptSortFile = new ScriptSortFile();
        scriptSortFile.setTestScriptFileList(getTableList());
        ScriptSortConfig.getInstance().setScriptSortFile(scriptSortFile);
        ScriptSortConfig.getInstance().save();
        mainModel.getTestScriptEventModel().changeSelectStatus(getCheckedRows());
    }

    @Override
    protected void swapTableList(Map<Integer, Integer> map) {
        for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
            int fromRow = entry.getKey();
            int toRow = entry.getValue();
            TestScriptFile testScriptFile = getRow(fromRow);
            getTableList().remove(fromRow);
            getTableList().add(toRow, testScriptFile);
            removeRowSelectionInterval(fromRow, fromRow);
            addRowSelectionInterval(toRow, toRow);
        }
    }


    private class addExecuteCaseActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            mainModel.getTestScriptEventModel().runScript(Arrays.stream(getSelectedRows()).boxed().collect(Collectors.toList()));
        }
    }

    private static class TestCycleSpinnerDialog extends SpinnerDialog<Integer> {
        private JCheckBox infiniteCycleCheckBox;

        public TestCycleSpinnerDialog(String title, String message, int initValue) {
            super(title, message, initValue);
        }

        @Override
        protected JComponent hookComponent() {
            infiniteCycleCheckBox = new JCheckBox("无限循环");
            infiniteCycleCheckBox.addItemListener(e -> getSpinner().setEnabled(!infiniteCycleCheckBox.isSelected()));
            return infiniteCycleCheckBox;
        }

        @Override
        public Integer getValue() {
            if (isValueValid()) {
                return infiniteCycleCheckBox.isSelected() ? -1 : getSpinnerValue();
            }
            return null;
        }
    }


    private class addModifyTestCycleListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            SpinnerDialog<Integer> dialog = new TestCycleSpinnerDialog("修改测试次数对话框", "测试次数", 1);
            Integer cycle = dialog.getValue();
            if (cycle != null) {
                List<TestScriptFile> testScriptFiles = getSelectedRowsData();
                for (TestScriptFile testScriptFile : testScriptFiles) {
                    TestScriptFileContent testScriptFileContent = new TestScriptFileContent();
                    testScriptFileContent.setUuid(testScriptFile.getUuid());
                    testScriptFileContent.setTestCycle(cycle);
                    OperationTargetHolder.getTestScriptKit().updateTestScriptTestCycle(testScriptFileContent);
                }
                if (!testScriptFiles.isEmpty()) {
                    mainModel.getTestScriptEventModel().refreshTestCycle(cycle);
                }
            }
        }
    }

    private class AddOpenFolderActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            TestScriptFile testScriptFile = getRow();
            String fileName = String.format("D:\\FlyTest\\data\\server\\projects\\%s\\database\\fileDB\\testcases", testScriptFile.getProjectName());
            try {
                Desktop.getDesktop().open(new File(fileName));
            } catch (IOException ex) {
                SwingUtil.showWarningDialog(null, String.format("文件夹无法打开:%s", ex.getMessage()));
            }
        }
    }

    private class AddOpenOrginalFileActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            TestScriptFile testScriptFile = getRow();
            File file = new File(String.format("D:\\FlyTest\\data\\server\\projects\\%s\\database\\fileDB\\testcases\\%s.json",
                    testScriptFile.getProjectName(),
                    FileUtils.replaceSpecialChar(testScriptFile.getCaseName())));
            try {
                if (file.exists() && file.canRead()) {
                    Desktop.getDesktop().open(file);
                }
            } catch (IOException ex) {
                SwingUtil.showWarningDialog(null, String.format("文件不存在或无法打开:%s", ex.getMessage()));
            }
        }
    }


    @Override
    protected void initData() {
        //初始化数据
        TestScriptFile testScriptFile = new TestScriptFile();
        testScriptFile.setProjectName(mainModel.getAppInfo().getProject());
        testScriptFile.setClientName(AppConstants.APP_NAME);
        JsonResponse<List<TestScriptFile>> resp = OperationTargetHolder.getTestScriptKit().fetchAllTestScriptFiles(testScriptFile);
        if (resp.isOk()) {
            List<TestScriptFile> fileList = resp.getData();
            List<TestScriptFile> testScriptSortList = ScriptSortConfig.getInstance().getScriptSortFile().getTestScriptFileList();
            List<TestScriptFile> sortedList = null;
            if (CollectionUtils.isNotEmpty(testScriptSortList)) {
                scriptEmpty = false;
                Map<String, Integer> map = new LinkedHashMap<>();
                for (int i = 0; i < testScriptSortList.size(); i++) {
                    map.put(testScriptSortList.get(i).getUuid(), i);
                }
                //FIXME:需要排查为什么会出现JSON与界面上uuid不一致的问题
                sortedList = fileList.stream().sorted(Comparator.comparingInt(o -> map.getOrDefault(o.getUuid(), 0))).collect(Collectors.toList());
            }

            TableLabelManager.getInstance().setProjectName(mainModel.getAppInfo().getProject());
            TableLabelManager.getInstance().setClientName(AppConstants.APP_NAME);

            // 从文件加载
            boolean loadedFromFile = TableLabelManager.getInstance().loadLabelsFromFile();

            // 从所有case遍历加载
            if (!loadedFromFile) {
                log.info("从文件加载标签失败，尝试从所有脚本加载...");
                List<Operation> operations = TableLabelManager.getInstance().loadOperationFromAllScripts();
                // 提取并加载
                TableLabelManager.getInstance().extractAndAddLabelsFromOperations(operations, false);
                // 保存
                TableLabelManager.getInstance().saveLabelsToFile();
            }

            for (TestScriptFile file : sortedList == null ? fileList : sortedList) {
                addRowData(file);
            }
        } else {
            SwingUtil.showWarningDialog(this, resp.getMessage(), "提醒", "获取自动化脚本失败:");
        }
    }

    /**
     * 脚本是否全选
     *
     * @return 是否全选
     */
    public boolean isAllSelected() {
        return !getTableList().isEmpty() && getTableList().stream().allMatch(TestScriptFile::isSelected);
    }

    @Override
    protected void setDefaultTableHeader() {

    }

    @Override
    public void setColumnWidth(int columnWidth) {

    }

    @Override
    public void createActions() {
        super.createActions();
        //点击表格
        addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                mainModel.getTestScriptEventModel().selectScript();
                super.mouseClicked(e);
            }
        });
        getSelectionModel().addListSelectionListener(e -> {
            if (e.getValueIsAdjusting()) {
                return;
            }
            selectRowAndSwitchScript();
        });
    }

    /**
     * 选中行并切换脚本
     */
    public void selectRowAndSwitchScript() {
        int currentSelectedRow = getSelectedRow();
        if (currentSelectedRow == lastSelectedRow) {
            return; // 同一行，不执行切换
        }

        lastSelectedRow = currentSelectedRow;
        if (currentSelectedRow != -1) {
            TestScriptFile testScriptFile = getRow(currentSelectedRow);
            log.info("切换到脚本:{} (第{}行)", testScriptFile.getCaseName(), currentSelectedRow + 1);
            projectConfiguration.getCaseConfig().setTestScriptId(testScriptFile.getId()).save();
            mainModel.getTestScriptEventModel().switchScript(testScriptFile);
            //更新之前case的测试结果
            TestScriptResultContext testScriptResultContext = TestScriptResultContext.getInstance();
            ExecutionResultReport executionResultReport = testScriptResultContext.getResult(testScriptFile.getUuid());
            if (executionResultReport != null) {
                mainModel.getTestScriptEventModel().updateResult(testScriptFile, executionResultReport);
            }
            mainModel.getTestScriptEventModel().adjustRowIndentation();
        }
        requestFocusInWindow();
    }

    @Override
    public void getPasteFocus() {
        List<TestScriptFile> clipBoard = tableActionManager.getClipBoard();
        if (!clipBoard.isEmpty()) {
            pasteRowsActivated(clipBoard);
            save();
            requestFocusInWindow();
        }
    }

    @Override
    public void clearClipBoard() {
        tableActionManager.getClipBoard().clear();
    }

    @Override
    public void clearClipBoardActivated() {
        mainModel.getClipBoardModel().clearClipBoard();
    }

    @Override
    protected void setPreferredColumn() {
        hiddenColumn(testScriptCaseId.getColumnIndex());
        getColumnModel().getColumn(testScriptCaseName.getColumnIndex()).setPreferredWidth(60);
    }

    @Override
    protected void setTableRenderer() {
        setDefaultRenderer(Object.class, new ColorTableRenderer());
        //为表格第一列设置checkbox渲染器，目的是为了渲染checkbox背景色
        CheckBoxCellRenderer checkBoxCellRenderer = new CheckBoxCellRenderer();
        getColumnModel().getColumn(0).setCellRenderer(checkBoxCellRenderer);
    }

    /**
     * 选择自动化脚本
     *
     * @param row      行
     * @param selected 是否选择
     */
    @Override
    public void setRowSelected(int row, boolean selected) {
        setValueAt(selected, row, 0);
        TestScriptFile testScriptFile = getRow(row);
        testScriptFile.setSelected(selected);
        OperationTargetHolder.getTestScriptKit().updateTestScriptFile(testScriptFile);
        //mainModel.getTestScriptEventModel().changeSelectStatus(getCheckedRows());
        save();
    }

    @Override
    protected void updateTableActivated(ActionEvent e) {
        super.updateTableActivated(e);//继承checkbox的处理逻辑
        TableCellListener tableCellListener = (TableCellListener) e.getSource();
        int row = tableCellListener.getRow();
        int column = tableCellListener.getColumn();
        if (column == testScriptCaseName.getColumnIndex()) {
            //脚本名称修改
            String newName = String.valueOf(tableCellListener.getNewValue()).trim();
            String oldName = String.valueOf(tableCellListener.getOldValue()).trim();
            if (newName.isEmpty()) {
                SwingUtil.showWarningDialog(this, "测试脚本名不能为空!");
                setValueAt(oldName, row, column);
            } else {
                if (!renameTestScriptName(newName)) {
                    setValueAt(oldName, row, column);
                }
            }
        } else if (column == testScriptCaseComment.getColumnIndex()) {
            //脚本注释修改
            String newComment = String.valueOf(tableCellListener.getNewValue()).trim();
            String oldName = String.valueOf(tableCellListener.getOldValue()).trim();
            if (newComment.equals(oldName)) return;
            setValueAt(newComment, row, column);
            TestScriptFile testScriptFile = getRow(getSelectedRow());
            testScriptFile.setComment(newComment);
            OperationTargetHolder.getTestScriptKit().updateTestScriptFile(testScriptFile);
        } else if (column == testScriptModuleName.getColumnIndex()) {
            //模块名称修改
            String newModule = String.valueOf(tableCellListener.getNewValue()).trim();
            String oldName = String.valueOf(tableCellListener.getOldValue()).trim();
            if (newModule.equals(oldName)) return;
            setValueAt(newModule, row, column);
            TestScriptFile testScriptFile = getRow(getSelectedRow());
            testScriptFile.setModuleName(newModule);
            OperationTargetHolder.getTestScriptKit().updateTestScriptFile(testScriptFile);
        }
        save();
    }

    public String getTestScriptName(int row) {
        return String.valueOf(getValueAt(row, testScriptCaseName.getColumnIndex()));
    }

    public void setTestScriptName(int row, String testScriptName) {
        setValueAt(testScriptName, row, testScriptCaseName.getColumnIndex());
    }

    public long getTestScriptId(int row) {
        return (long) getValueAt(row, testScriptCaseId.getColumnIndex());
    }

    private void notifyTestScriptClear() {
        mainModel.getTestScriptEventModel().clearScript();
        projectConfiguration.getCaseConfig().setTestScriptId(-1L).save();
    }

    @Override
    public int deleteRow(int row) {
        int currentRow = super.deleteRow(row);
        if (currentRow < 0) {
            notifyTestScriptClear();
        }
        return currentRow;
    }

    @Override
    public void deleteRowsActivated(int[] rows) {
        deleteTestScript(rows);
        save();
    }

    @Override
    public boolean clearTableActivated() {
        int result = JOptionPane.showConfirmDialog(this, "确定清空所有脚本？（该操作无法恢复，请谨慎！）", "清空表格", JOptionPane.YES_NO_OPTION);
        if (result == JOptionPane.YES_OPTION) {
            return clearAllScriptCase(true);
        }
        return false;
    }

    public boolean clearAllScriptCase() {
        return clearAllScriptCase(false);
    }

    private boolean clearAllScriptCase(boolean tips) {
        TestScriptFile testScriptFile = new TestScriptFile();
        testScriptFile.setClientName(AppConstants.APP_NAME);
        testScriptFile.setProjectName(mainModel.getAppInfo().getProject());
        JsonResponse<String> resp = OperationTargetHolder.getTestScriptKit().clearAllTestScriptFiles(testScriptFile);
        if (resp.isOk()) {
            clearTable();
            notifyTestScriptClear();
            scriptEmpty = true;
            Container parent = this.getParent();
            while (parent != null && !(parent instanceof TestScriptCaseTabPaneView)) {
                parent = parent.getParent();
            }
            if (parent != null) {
                ((TestScriptCaseTabPaneView) parent).updatePanelVisibility(scriptEmpty);
            }
            return true;
        } else {
            if (!tips) {
                mainModel.getNotificationScriptStatusModel().notification(new RemoteOperation(RemoteOperationStatus.CLEAR_SCRIPT_FAILED, resp.getMessage()));
                return false;
            }
            SwingUtil.showWebMessageDialog(this, resp.getMessage());
            return false;
        }
    }

    /**
     * 重命名自动化脚本
     *
     * @param newTestScriptName 自动化脚本名
     * @return 是否重命名成功
     */
    public boolean renameTestScriptName(String newTestScriptName) {
        TestScriptFile testScriptFile = getRow(getSelectedRow());
        testScriptFile.setCaseName(newTestScriptName);
        JsonResponse<TestScriptFile> resp = OperationTargetHolder.getTestScriptKit().renameTestScriptFile(testScriptFile);
        if (resp.isOk()) {
            mainModel.getTestScriptEventModel().switchScript(testScriptFile);
            setTestScriptName(getSelectedRow(), newTestScriptName);
            save();
            return true;
        } else {
            SwingUtil.showWebMessageDialog(this, resp.getMessage());
            return false;
        }
    }


    /**
     * 删除自动化脚本
     *
     * @param allRow 选择行
     * @return 是否删除成功
     */
    public boolean deleteTestScript(int[] allRow) {
        List<String> allTestScriptName = new ArrayList<>();
        for (int selectRow : allRow) {
            String testScriptName = getTestScriptName(selectRow) + "\n";
            allTestScriptName.add(testScriptName);
        }
        String message = String.join("", allTestScriptName);
        int isDelete = JOptionPane.showConfirmDialog(this, String.format("确定要删除以下用例吗\n%s", message), "删除文件提醒", JOptionPane.YES_NO_OPTION);
        boolean result = false;
        if (isDelete == JOptionPane.YES_OPTION) {
            for (int selectRow : allRow) {
                String testScriptName = getTestScriptName(selectRow);
                TestScriptFile testScriptFile = getRow(selectRow);
                JsonResponse<String> resp = OperationTargetHolder.getTestScriptKit().deleteTestScriptFile(testScriptFile.getUuid());
                if (resp.isOk()) {
                    mainModel.getTestScriptEventModel().deleteScript(testScriptName);
                    deleteRow(selectRow);
                    result = true;
                } else {
                    SwingUtil.showWebMessageDialog(this, resp.getMessage());
                }
            }
        }
        return result;
    }

    @Override
    public void selectAllEvent(boolean isSelectAll) {
        TestScriptFileSelector testScriptFileSelector = new TestScriptFileSelector();
        testScriptFileSelector.setProjectName(mainModel.getAppInfo().getProject());
        testScriptFileSelector.setClientName(AppConstants.APP_NAME);
        testScriptFileSelector.setSelectAll(isSelectAll);
        OperationTargetHolder.getTestScriptKit().selectAll(testScriptFileSelector);
        // 将表格中的选中状态保存到 scriptSortConfig.json
        List<TestScriptFile> tableList = getTableList();
        tableList.forEach(testScriptFile -> {
//                System.out.println(testScriptFile.isSelected());
            testScriptFile.setSelected(isSelectAll);
        });
        save();
    }

    /**
     * 导出自动化脚本
     *
     * @return 是否导出
     */
    public boolean exportTestScript() {
        List<TestScriptFile> selectedRowsData = getAllRowsData();
        List<TestScriptFile> list = new ArrayList<>();
        for (TestScriptFile rowData : selectedRowsData) {
            if (rowData.isSelected()) {
                list.add(rowData);
            }
        }
        File saveFile = SwingUtil.getFileChooser(this, "导出", new FileNameExtensionFilter("JSON文件", "json"), false);
        assert saveFile != null;
        return true;
    }

    @Override
    public void switchScript(int scriptRow) {
        log.info("切换到第{}行", scriptRow + 1);
        selectRow(scriptRow);
        SwingUtil.invokeLater(() -> setColor(scriptRow, ExcelConstants.TESTING_COLOR));
    }

    public TestResultReport report(Map<Integer, ExecuteResult> executeResults) {
        TestResultReport testResultReport = new TestResultReport();
        int sumCount = executeResults.size();
        int passCount = (int) executeResults.values().stream().filter(ExecuteResult::isOk).count();
        int failCount = sumCount - passCount;
        testResultReport.setSumCount(sumCount);
        testResultReport.setPassCount(passCount);
        testResultReport.setFailCount(failCount);
        return testResultReport;
    }


    @Override
    public void singleCaseCompleted(ExecutionNotification executionNotification) {
        int testRow = executionNotification.getExecutionIndex();
        TestResultReport testResultReport = executionNotification.getExecutionResultReport().getTestResultReport();
        log.info("更新脚本结果报告:{}(第{}行)->{}", executionNotification.getCaseName(), testRow, testResultReport);
        updateTestScriptResult(testRow, testResultReport);
    }

    private void updateTestScriptResult(int testRow, TestResultReport testResultReport) {
        if (testResultReport == null) {
            return;
        }
        if (testResultReport.isPass()) {
            setColor(testRow, ExcelConstants.TEST_PASS_COLOR);
        } else {
            setColor(testRow, ExcelConstants.TEST_FAIL_COLOR);
        }
        setValueAt(String.format("%s", testResultReport), testRow, testScriptResult.getColumnIndex());
    }

    /**
     * 根据行运行脚本
     *
     * @param executionSuite 脚本测试套件
     */
    public void runScriptsByRow(ExecutionSuite executionSuite, TestResultListener testResultListener) {
        mainModel.getTestScriptEventModel().executeTestSuite(executionSuite, testResultListener);
    }

    public void disableExecuteCaseMenu() {
        JMenuItem executeCaseMenuItem = findMenuItemByName("执行当前用例");
        if (executeCaseMenuItem != null) {
            executeCaseMenuItem.setEnabled(false);
        }
    }

    public void enableExecuteCaseMenu() {
        JMenuItem executeCaseMenuItem = findMenuItemByName("执行当前用例");
        if (executeCaseMenuItem != null) {
            executeCaseMenuItem.setEnabled(true);
        }
    }

    public void checkedAll(boolean isSelectAll) {
        programmableSelectAll(isSelectAll);
        visionSelectAll(isSelectAll);
    }

    public boolean isScriptEmpty() {
        return scriptEmpty;
    }

}
