package ui.layout.left.display.components.container.electric_relay;

import llm.SystemPrompt;
import sdk.base.operation.Operation;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.ChannelSwitch;
import sdk.domain.Device;
import sdk.entity.ElectricRelayDevice;
import ui.config.json.devices.electric_relay.ElectricRelayConfig;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import java.awt.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ElectricRelayContainer extends DeviceContainer {
    private final ElectricRelayDevice electricRelayDevice;
    private final ElectricRelayConfig electricRelayConfig;
    private JTextField delayTimeField; // 统一延迟时间输入框

    public ElectricRelayContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
        electricRelayConfig = ((ElectricRelayDevice) device).loadConfig(mainModel.getAppInfo().getProject());
        electricRelayDevice = (ElectricRelayDevice) device;

        // 添加主容器，使用BorderLayout
        setLayout(new BorderLayout());

        Box box = Box.createVerticalBox();
        if (electricRelayConfig.getPhyText() == null || electricRelayConfig.getPhyText().isEmpty()) {
            // 如果列表为空，使用默认值初始化
            List<String> defaultPhyText = new ArrayList<>(Collections.nCopies(16, ""));
            for (int i = 1; i <= 16; i++) {
                switch (i) {
                    case 1:
                        defaultPhyText.set(0, "点火KL15");
                        break;
                    case 2:
                        defaultPhyText.set(i - 1, "蓄电池KL30");
                        break;
                    case 3:
                        defaultPhyText.set(i - 1, "ACC");
                        break;
                    default:
                        defaultPhyText.set(i - 1, ""); // 其他通道默认为空
                }
            }
            electricRelayConfig.setPhyText(defaultPhyText);
            saveConfig();
        } else if (electricRelayConfig.getPhyText().size() < 16) {
            for (int i = electricRelayConfig.getPhyText().size(); i < 16; i++) {
                electricRelayConfig.getPhyText().add("");
            }
        }

        for (int i = 1; i <= 16; i++) {
            String phyLabel = electricRelayConfig.getPhyText().get(i - 1);
            box.add(createRow(i, phyLabel));
        }

        // 添加通道面板到中间区域
        JScrollPane scrollPane = new JScrollPane(box);
        add(scrollPane, BorderLayout.CENTER);

        // 创建延迟时间设置面板
        JPanel delayPanel = createDelayPanel();
        add(delayPanel, BorderLayout.SOUTH);
    }

    private JPanel createDelayPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setBorder(BorderFactory.createTitledBorder("延迟时间设置"));

        panel.add(new JLabel("统一延迟时间(ms):"));
        delayTimeField = new JTextField(10);
        delayTimeField.setText("0"); // 默认值
        panel.add(delayTimeField);

        return panel;
    }

    private JPanel createRow(int channelOrder, String phyLabel) {
        JPanel panel = new JPanel();
        panel.add(new JLabel(String.format("通道%2d", channelOrder)));

        JTextField phyTextField = new JTextField(5);
        panel.add(phyTextField);

        // 先添加监听器
        phyTextField.getDocument().addDocumentListener(new DocumentListener() {
            @Override
            public void insertUpdate(DocumentEvent e) {
                updatePhyText(channelOrder, phyTextField.getText());
            }

            @Override
            public void removeUpdate(DocumentEvent e) {
                updatePhyText(channelOrder, phyTextField.getText());
            }

            @Override
            public void changedUpdate(DocumentEvent e) {
                updatePhyText(channelOrder, phyTextField.getText());
            }
        });

        // 然后设置文本
        phyTextField.setText(phyLabel);

        JToggleButton switchButton = new JToggleButton();
        switchButton.setText("OFF");
        switchButton.addActionListener(e -> {
            if (switchButton.isSelected()) {
                switchButton.setText("ON");
            } else {
                switchButton.setText("OFF");
            }
        });
        panel.add(switchButton);
        switchButton.addActionListener(e -> {
            new SwingWorker<Void, Void>() {
                @Override
                protected Void doInBackground() throws Exception {
                    ChannelSwitch channelSwitch = new ChannelSwitch();
                    channelSwitch.setChannel(channelOrder);
                    channelSwitch.setSwitchOn(switchButton.isSelected());
                    channelSwitch.setPhyLabel(phyTextField.getText());
                    channelSwitch.setDelayTime(delayTimeField.getText()); // 设置延迟时间
                    electricRelayDevice.switchRelay(channelSwitch);
                    return null;
                }
            }.execute();
        });

        JButton addToScriptButton = SwingUtil.getAddToScriptButton();
        panel.add(addToScriptButton);
        addToScriptButton.addActionListener(e -> {
            Operation operation = Operation.buildOperation(electricRelayDevice);
            ChannelSwitch channelSwitch = new ChannelSwitch();
            channelSwitch.setChannel(channelOrder);
            channelSwitch.setSwitchOn(switchButton.isSelected());
            channelSwitch.setPhyLabel(phyTextField.getText());
            channelSwitch.setDelayTime(delayTimeField.getText()); // 设置延迟时间
            operation.setOperationMethod(DeviceMethods.switchRelay);
            operation.setOperationObject(channelSwitch);
            operation.setFriendlyOperationObject(channelSwitch.getPhyLabel());
            getMainModel().getOperationModel().updateOperation(operation);
        });

        return panel;
    }

    private void updatePhyText(int channelOrder, String newText) {
        electricRelayConfig.getPhyText().set(channelOrder - 1, newText);
        saveConfig();
    }

    private void saveConfig() {
        electricRelayDevice.updateConfig(electricRelayConfig);
        SystemPrompt.getInstance().setElectricRelayConfig(electricRelayConfig.getConfig());
    }
}
