package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import lombok.Getter;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.io.File;

public class SaveLogDialog extends JDialog {
    private JCheckBox enableCheckBox;
    private JCheckBox testLogCheckBox;
    private JTextField folderPathField;
    private JButton selectButton;
    private JButton okButton;
    private static SaveLogDialog instance;
    private MainModel mainModel;

    @Getter
    private boolean confirmed = false;


    public static SaveLogDialog getInstance(MainModel mainModel) {
        if (instance == null) {
            instance = new SaveLogDialog(mainModel);
        }
        return instance;
    }

    public SaveLogDialog(MainModel mainModel) {
        this.mainModel = mainModel;
        initializeUI();
    }

    private void initializeUI() {
        setTitle("按case保存日志设置");
        setLayout(new GridLayout(4, 1, 10, 10));
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));

        enableCheckBox = new JCheckBox("开启按case保存");
        testLogCheckBox = new JCheckBox("开启Test Log保存");
        folderPathField = new JTextField(20);
        selectButton = new JButton("选择文件夹");
        okButton = new JButton("确定");

        selectButton.addActionListener(this::selectFolder);
        okButton.addActionListener(this::confirmAndClose);

        panel.add(enableCheckBox);
        panel.add(testLogCheckBox);
        add(panel);

        JPanel inputPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        inputPanel.add(new JLabel("文件夹路径:"));
        inputPanel.add(folderPathField);
        inputPanel.add(selectButton);
        add(inputPanel);

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        buttonPanel.add(okButton);
        add(buttonPanel);

        setSize(500, 200);
        setLocationRelativeTo(null); // 居中显示
        setDefaultCloseOperation(DISPOSE_ON_CLOSE);
        enableCheckBox.addActionListener(e -> {
            boolean selected = enableCheckBox.isSelected();
            if (selected) {
                if (isFolderPathEmpty()) {
                    JOptionPane.showMessageDialog(this, "请先选择文件夹路径", "提示", JOptionPane.WARNING_MESSAGE);
                    enableCheckBox.setSelected(false);
                    return;
                }
            }
            mainModel.getAppInfo().setEnableUserLog(selected);
        });


    }

    private boolean isFolderPathEmpty() {
        String path = folderPathField.getText().trim();
        if (path.isEmpty()) {
            folderPathField.setBorder(BorderFactory.createLineBorder(Color.RED));
            return true;
        } else {
            folderPathField.setBorder(UIManager.getBorder("TextField.border"));
            return false;
        }
    }

    private void selectFolder(ActionEvent e) {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        int result = fileChooser.showOpenDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            File selectedFolder = fileChooser.getSelectedFile();
            folderPathField.setText(selectedFolder.getAbsolutePath());
            mainModel.getAppInfo().setUserLogPath(selectedFolder.getAbsolutePath());
        }
    }

    private void confirmAndClose(ActionEvent e) {
        confirmed = true;
        setVisible(false);
    }

    public boolean isEnableSaveByCase() {
        return enableCheckBox.isSelected();
    }

    public boolean isSaveTestLog() {
        return testLogCheckBox.isSelected();
    }

    public String getFolderPath() {
        return folderPathField.getText();
    }

}
