package ui.layout.left.display.components.container.picture;

import common.constant.ResourceConstant;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import sdk.domain.Device;
import sdk.entity.interfaces.IVision;
import ui.base.picture.PicturePanel;
import ui.base.picture.PictureRectDrawLabel;
import ui.callback.PictureEventHandler;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.picture.test.AreaEventHandler;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-2 11:12
 * @description :
 * @modified By :
 * @since : 2022-4-2
 */
@Slf4j
public abstract class PictureContainer extends DeviceContainer implements PictureEventHandler, AreaEventHandler {
    private static final long serialVersionUID = -2216276219179107707L;

    @Getter
    private PicturePanel picturePanel;
    @Getter
    private Box toolBox;
    private final MainModel mainModel;
    private JLabel playOrPauseButton;
    private ImageIcon streamPlayIcon;
    private ImageIcon streamPauseIcon;
    @Getter
    @Setter
    private boolean streamPausing = false;
    @Getter
    private final boolean alwaysDynamic;

    private PictureRectDrawLabel pictureRectDrawLabel;

    public interface GrabCallback {

        void grabStarted();

        void grabCompleted();

    }

    public void enablePlayOrPauseButton(boolean enabled) {
        playOrPauseButton.setEnabled(enabled);
    }

    private final List<GrabCallback> grabCallbacks;

    public void addGrabCallback(GrabCallback callback) {
        grabCallbacks.add(callback);
    }

    public void setDevice(Device device) {
        picturePanel.setDevice(device);
    }

    public PictureContainer(ClientView clientView, MainModel mainModel, Device device) {
        this(clientView, mainModel, device, false);
    }

    /**
     * PictureContainer构造器
     *
     * @param mainModel     MainModel
     * @param device        相机
     * @param alwaysDynamic 是否始终输出动态图像
     */
    public PictureContainer(ClientView clientView, MainModel mainModel, Device device, boolean alwaysDynamic) {
        super(clientView, mainModel, device);
        this.mainModel = mainModel;
        this.alwaysDynamic = alwaysDynamic;
        grabCallbacks = new ArrayList<>();
        createView();
        createActions();
        setDevice(device);
        createMenu();
        if (alwaysDynamic) {
            playOrPauseButton.setEnabled(false);
        }
        registerModelObservers();
    }

    protected void setPlayOrPauseButtonVisible(boolean visible) {
        playOrPauseButton.setVisible(visible);
    }


    /**
     * 获取实际画板
     *
     * @return 实际画板
     */
    public PictureRectDrawLabel getDrawingBoard() {
        return picturePanel.getImageLabel();
    }

    public void createMenu() {
        picturePanel.createMenu();
    }

    public void addMenu(String menuName, ActionListener actionListener) {
        picturePanel.addMenu(menuName, actionListener);
    }

    protected void initPlayOrPauseButton() {
        playOrPauseButton = new JLabel();
        streamPlayIcon = SwingUtil.getResourceAsImageIcon(ResourceConstant.LeftLayout.streamPlayIconPath);
        streamPauseIcon = SwingUtil.getResourceAsImageIcon(ResourceConstant.LeftLayout.streamPauseIconPath);
        enablePauseButton();
        playOrPauseButton.setPreferredSize(new Dimension(streamPauseIcon.getIconWidth(),
                streamPauseIcon.getIconHeight()));
        playOrPauseButton.setOpaque(true);
        playOrPauseButton.setHorizontalAlignment(JLabel.CENTER);
    }

    protected List<JLabel> toolButtonList() {
        return Collections.singletonList(playOrPauseButton);
    }

    protected PictureRectDrawLabel getPictureRectDrawLabel() {
        return new PictureRectDrawLabel(mainModel, this);
    }

    @Override
    public void createView() {
        initPlayOrPauseButton();
        setLayout(new BorderLayout());
        pictureRectDrawLabel = getPictureRectDrawLabel();
        picturePanel = new PicturePanel(mainModel, pictureRectDrawLabel);
        add(picturePanel, BorderLayout.CENTER);
        toolBox = Box.createHorizontalBox();
        toolBox.add(Box.createHorizontalGlue());
        for (JLabel toolButton : toolButtonList()) {
            toolBox.add(toolButton);
        }
        toolBox.add(Box.createHorizontalGlue());
        add(toolBox, BorderLayout.SOUTH);
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        super.controlDisplay(isDeviceConnected);
        playOrPauseButton.setEnabled(isDeviceConnected);
        if (isDeviceConnected) {
            enablePauseButton();
            grab();
        }
    }

    @Override
    public void switchDrawInterestArea(boolean draw) {
        pictureRectDrawLabel.setDrawInterestArea(draw);
    }

    @Override
    public void clearAllAreas() {
        pictureRectDrawLabel.clearAllAreas();
    }

    public void setPlayOrPauseButtonEnabled(boolean isEnabled) {
        playOrPauseButton.setVisible(isEnabled);
    }

    protected void enablePlayButton() {
        //此时画面静态
        streamPausing = true;
        playOrPauseButton.setIcon(streamPlayIcon);
    }

    protected void enablePauseButton() {
        //此时画面动态
        streamPausing = false;
        playOrPauseButton.setIcon(streamPauseIcon);
    }

    /**
     * 开始采集图像
     */
    protected void startGrab() {
        for (GrabCallback callback : grabCallbacks) {
            callback.grabStarted();
        }
        getDrawingBoard().setEnablePainting(false);
    }

    /**
     * 结束采集图像
     */
    protected void completeGrab() {
        for (GrabCallback callback : grabCallbacks) {
            callback.grabCompleted();
        }
        getDrawingBoard().setEnablePainting(true);
    }

    @Override
    public void createActions() {
        //        addHierarchyListener(new HierarchyListener() {
//            @Override
//            public void hierarchyChanged(HierarchyEvent e) {
//                if ((e.getChangeFlags() & HierarchyEvent.SHOWING_CHANGED) != 0) {
//                    if (isShowing()) {
//                        System.out.println("Component is now visible.");
//                        // 可见性变化后的操作
//                    } else {
//                        System.out.println("Component is now hidden.");
//                        // 可见性变化后的操作
//                    }
//                }
//            }
//        });
        if (!alwaysDynamic) {
            playOrPauseButton.addMouseListener(new MouseAdapter() {
                @Override
                public void mouseClicked(MouseEvent e) {
//                    System.out.println("streamPausing:" + streamPausing);
                    outputDynamicStream(streamPausing);
                    playOrPause();
                }
            });
        }
    }

    public void playOrPause() {

    }

    @Override
    public void pictureDrawingStart() {
        outputDynamicStream(false);
    }

    @Override
    public void pictureDrawingEnd() {

    }

    @Override
    public void pictureOperationCompleted() {

    }

    protected void outputDynamicStream(boolean isOutput) {
        if (getDevice() != null) {
            IVision visionDevice = (IVision) getDevice();
            if (isAlwaysDynamic()) {
                visionDevice.outputDynamicFrame(true);
                return;
            }
            if (isOutput) {
                visionDevice.outputDynamicFrame(true);
                enablePauseButton();
            } else {
                if (!streamPausing) {
                    visionDevice.outputDynamicFrame(false);
                }
                enablePlayButton();
            }
        }
    }

    public abstract void grab();

}
