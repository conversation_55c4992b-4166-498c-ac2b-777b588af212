package ui.layout.left.display.components.container.serial;

import org.apache.commons.lang3.StringUtils;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationTarget;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.MessageText;
import sdk.domain.serial.SerialJudgeParameter;
import sdk.domain.serial.WaitFilterParameter;
import sdk.entity.SerialDevice;
import ui.base.BaseView;
import ui.base.PlaceholderTextField;
import ui.config.json.devices.serial.SerialConfig;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.base.DeviceControlPanel;
import ui.model.MainModel;
import ui.model.device.DeviceSendDataObserver;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.LineBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;

/**
 * 串口控制面板
 */
public class SerialControlPanel extends DeviceControlPanel implements BaseView, DeviceSendDataObserver {
    private final JCheckBox isHexCheckBox;
    private final JCheckBox autoScrollCheckBox;
    private final JCheckBox autoLogCheckBox;
    private final PlaceholderTextField waitFilterTextField;
    private final JSpinner waitFilterTimeoutSpinner;
    private final JComboBox<String> judgeComboBox;
    private final JSpinner judgeTimeoutSpinner;
    private final JTextField judgeTextField;
    private final JTextField mustExistTextField;
    private final JTextField forbidExistTextField;
    private final JButton waitFilterAddToScriptButton;
    private final JButton mustExistAddToScriptButton;
    private final JButton forbidExistAddToScriptButton;
    private final JButton judgeAddToScriptButton;

    private final SerialConfig serialConfig;
    private final SerialDevice serialDevice;
    private final SendAndMatchPanel sendAndMatchPanel;

    public SerialControlPanel(DeviceContainer deviceContainer, MainModel mainModel, SerialConfig serialConfig) {
        super(deviceContainer, mainModel);
        this.serialConfig = serialConfig;
        sendAndMatchPanel = new SendAndMatchPanel(this);
        serialDevice = (SerialDevice) deviceContainer.getDevice();
        isHexCheckBox = new JCheckBox("十六进制发送/接收");
        autoScrollCheckBox = new JCheckBox("自动滚动");
        autoScrollCheckBox.setSelected(true);
        autoLogCheckBox = new JCheckBox("自动保存日志");
        waitFilterTextField = new PlaceholderTextField("输入等待过滤文字...");
        mustExistTextField = new JTextField();
        waitFilterAddToScriptButton = addNewScriptButton();
        mustExistAddToScriptButton = addNewScriptButton();
        forbidExistTextField = new JTextField();
        forbidExistAddToScriptButton = addNewScriptButton();
        waitFilterTimeoutSpinner = new JSpinner(new SpinnerNumberModel(1, 1, Integer.MAX_VALUE, 1));
        judgeComboBox = new JComboBox<>(new String[]{"=", ">", "<", ">=", "<="});
        judgeTimeoutSpinner = new JSpinner(new SpinnerNumberModel(50, 1, Integer.MAX_VALUE, 1));
        judgeTextField = new JTextField();
        judgeAddToScriptButton = addNewScriptButton();
        createView();
        createActions();
        restoreView();
        registerModelObservers();
    }

    @Override
    public void registerModelObservers() {
        getMainModel().getDeviceSendDataModel().registerObserver(this);
    }

    @Override
    public void sendMessage(MessageText messageText) {
        serialDevice.send(messageText);
    }

    public boolean isHex() {
        return isHexCheckBox.isSelected();
    }

    public boolean isAutoScroll() {
        return autoScrollCheckBox.isSelected();
    }


    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        sendAndMatchPanel.controlDisplay(isDeviceConnected);
    }

    @Override
    public void createView() {
        setLayout(new GridLayout(1, 1));
        Box mainBox = Box.createVerticalBox();

        JPanel procedureCheckPanel = new JPanel(new GridLayout(4, 1));
        Box waitFilterBox = Box.createHorizontalBox();
        waitFilterBox.add(new JLabel("等待出现:"));
        waitFilterBox.add(waitFilterTextField);
        waitFilterBox.add(new JLabel("等该超时(秒):"));
        waitFilterBox.add(waitFilterTimeoutSpinner);
        waitFilterBox.add(waitFilterAddToScriptButton);

        Box mustAppearBox = Box.createHorizontalBox();
        mustAppearBox.add(new JLabel("必须出现:"));
        mustAppearBox.add(mustExistTextField);
        mustAppearBox.add(mustExistAddToScriptButton);

        Box forbidAppearBox = Box.createHorizontalBox();
        forbidAppearBox.add(new JLabel("禁止出现:"));
        forbidAppearBox.add(forbidExistTextField);
        forbidAppearBox.add(forbidExistAddToScriptButton);

        Box compareAppearBox = Box.createHorizontalBox();
        compareAppearBox.add(new JLabel("比较结果:"));
        compareAppearBox.add(judgeComboBox);
        compareAppearBox.add(judgeTextField);
        compareAppearBox.add(new JLabel("等待超时(秒):"));
        compareAppearBox.add(judgeTimeoutSpinner);
        compareAppearBox.add(judgeAddToScriptButton);

        procedureCheckPanel.setBorder(BorderFactory.createTitledBorder(new LineBorder(new Color(0, 0, 0)), "过程检测", TitledBorder.LEADING, TitledBorder.TOP, null, null));

        procedureCheckPanel.add(waitFilterBox);
        procedureCheckPanel.add(mustAppearBox);
        procedureCheckPanel.add(forbidAppearBox);
        procedureCheckPanel.add(compareAppearBox);

        Box box1 = Box.createHorizontalBox();
        box1.add(isHexCheckBox);
        box1.add(autoScrollCheckBox);
        box1.add(autoLogCheckBox);
        mainBox.add(box1);
        mainBox.add(procedureCheckPanel);
        mainBox.add(sendAndMatchPanel);
        add(mainBox);
    }

    @Override
    public void restoreView() {
        isHexCheckBox.setSelected(serialConfig.isSendOrReceiveWithHex());
        autoLogCheckBox.setSelected(serialConfig.isSaveLogFlag());
    }

    @Override
    public void createActions() {
        isHexCheckBox.addItemListener(e -> {
            serialConfig.setSendOrReceiveWithHex(isHexCheckBox.isSelected());
            serialDevice.updateConfig(serialConfig);
            sendAndMatchPanel.changeDocumentFilter(isHexCheckBox.isSelected());
        });
        mustExistAddToScriptButton.addActionListener(e -> mustExistAddToScript());
        forbidExistAddToScriptButton.addActionListener(e -> forbidExistAddToScript());
        autoLogCheckBox.addItemListener(e -> {
            serialConfig.setSaveLogFlag(autoLogCheckBox.isSelected());
            serialDevice.updateConfig(serialConfig);
        });
        waitFilterAddToScriptButton.addActionListener(e -> waitFilter(waitFilterTextField.getText(), (Integer) waitFilterTimeoutSpinner.getValue()));
        judgeAddToScriptButton.addActionListener(e -> judgeText());
    }

    private void judgeText() {
        String text = judgeTextField.getText();
        if (StringUtils.isBlank(text)) {
            SwingUtil.showWarningDialog(this, "检测文字不能为空");
            return;
        }
        Operation operation = Operation.buildOperation(getDeviceContainer().getDevice());
        operation.setOperationMethod(DeviceMethods.judgeText);
        SerialJudgeParameter serialJudgeParameter = new SerialJudgeParameter();
        serialJudgeParameter.setOperator(judgeComboBox.getSelectedIndex());
        serialJudgeParameter.setContent(text);
        serialJudgeParameter.setTimeout((Integer) judgeTimeoutSpinner.getValue());
        operation.setOperationObject(serialJudgeParameter);
        getMainModel().getOperationModel().updateOperation(operation);
    }

    private void waitFilter(String text, int timeout) {
        Operation operation = Operation.buildOperation(getDeviceContainer().getDevice());
        operation.setOperationMethod(DeviceMethods.waitFilter);
        WaitFilterParameter waitFilterParameter = new WaitFilterParameter();
        waitFilterParameter.setText(text);
        waitFilterParameter.setTimeout(timeout);
        operation.setOperationObject(waitFilterParameter);
        getMainModel().getOperationModel().updateOperation(operation);
    }

    private void mustExistAddToScript() {
        Operation operation = Operation.buildOperation(getDeviceContainer().getDevice());
        OperationMethod operationMethod = null;
        String text = mustExistTextField.getText().trim();
        if (!StringUtils.isBlank(text)) {
            operationMethod = DeviceMethods.mustExistMonitorStart;
        } else {
            SwingUtil.showWarningDialog(this, "检测文字不能为空");
        }
        operation.setOperationObject(text);
        operation.setOperationMethod(operationMethod);
        getMainModel().getOperationModel().updatePairedOperation(operation);
    }

    private void forbidExistAddToScript() {
        Operation operation = new Operation();
        OperationTarget operationTarget = OperationTarget.ofDevice(getDeviceContainer().getDevice());
        operation.setOperationTarget(operationTarget);
        OperationMethod operationMethod = null;
        String text = forbidExistTextField.getText().trim();
        if (!StringUtils.isBlank(text)) {
            operationMethod = DeviceMethods.forbidExistMonitorStart;
        } else {
            SwingUtil.showWarningDialog(this, "检测文字不能为空");
        }
        operation.setOperationObject(text);
        operation.setOperationMethod(operationMethod);
        getMainModel().getOperationModel().updatePairedOperation(operation);
    }

}
