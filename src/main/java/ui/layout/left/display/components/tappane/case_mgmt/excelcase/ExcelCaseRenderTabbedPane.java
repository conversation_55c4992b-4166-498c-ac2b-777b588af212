package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import excelcase.AutoScrollPane;
import excelcase.config.json.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import sdk.domain.excel.ExcelSheetData;
import sdk.entity.OperationTargetHolder;
import ui.base.BaseView;
import ui.base.CopyableLabel;
import ui.base.cosntants.ColumnNameConstants;
import ui.base.table.FrozenColumnTable;
import ui.entry.ClientView;
import ui.layout.right.components.testcase.TestStep;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.plaf.basic.BasicScrollBarUI;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static common.constant.UiConstants.HEADER_DEFAULT_COLUMN_WIDTH;
import static common.utils.ListUtils.findCommonElement;
import static ui.base.table.checkbox.CheckboxTable.SELECT_ALL_TEXT;
import static ui.base.table.rowHeader.RowHeaderTable.ADJUST_WIDTH;

/**
 * Excel Case标签卡面板
 * TODO: 2024/11/15：优化加载和同步Excel性能耗时——LHY
 */
@Slf4j
public class ExcelCaseRenderTabbedPane extends JTabbedPane implements BaseView {
    private final MainModel mainModel;
    private final ClientView clientView;
    private final List<ExcelCaseTable> excelCaseTableList; //CaseTable列表
    @Getter
    private final ExcelCaseTabPaneView excelCaseTabPaneView;
    @Getter
    private final Map<String, ExcelCaseTable> tableMap;
    private final Map<ExcelCaseTable, List<String>> syncMap;

    public ExcelCaseRenderTabbedPane(ExcelCaseTabPaneView excelCaseTabPaneView, ClientView clientView, MainModel mainModel) {
        this.mainModel = mainModel;
        this.clientView = clientView;
        this.excelCaseTabPaneView = excelCaseTabPaneView;
        tableMap = new LinkedHashMap<>();
        syncMap = new LinkedHashMap<>();
        excelCaseTableList = new ArrayList<>();
        createActions();
    }

    /**
     * 获取已选择的Excel Case行
     *
     * @return 已选择的Excel Case行
     */
    public List<Integer> getSelectedExcelRows() {
        List<Integer> excelRowList = new ArrayList<>();
        for (ExcelCaseTable table : excelCaseTableList) {
            excelRowList.addAll(table.getCheckedRows());
        }
        return excelRowList;
    }

    @Override
    public void createView() {

    }

    @Override
    public void createActions() {
        //切换tab时同步excel case和动作序列面板
        addChangeListener(e -> syncCaseAndStepTable());
    }

    /**
     * 同步excel case和动作序列面板
     */
    public void syncCaseAndStepTable() {
        ExcelCaseTable selectedExcelCaseTable = getSelectedExcelCaseTable();
        if (selectedExcelCaseTable != null) {
            int selectedRow = selectedExcelCaseTable.getSelectedRow();
            //转换成测试序列步骤列表
            List<TestStep> testStepList = selectedExcelCaseTable.convertAllTestStepData(selectedRow);
            //通知切换测试序列步骤列表
            if (testStepList != null && !testStepList.isEmpty()) {
                mainModel.getTestStepModel().groupingByStepType(testStepList);
                mainModel.getTestStepModel().switchTestStep("ALL");
            }
        }
    }

    private List<String> validateExcelTable(Map<String, ExcelSheetData> dataMap) {
        List<String> vaildSheetNameList = new ArrayList<>();
        for (Map.Entry<String, ExcelSheetData> entry : dataMap.entrySet()) {
            String sheetName = entry.getKey();
            List<String> tableHeaderList = entry.getValue().getTableHeader();
            boolean valid = tableFormatCorrect(tableHeaderList);
            entry.getValue().setValid(valid);
            if (valid) {
                vaildSheetNameList.add(sheetName);
            }
        }
        return vaildSheetNameList;
    }

    /**
     * 渲染Excel Case映射数据
     *
     * @param dataMap 映射数据
     */
    public void handleData(Map<String, ExcelSheetData> dataMap, boolean manualImport) {
        if (dataMap == null) {
            return;
        }
        //验证表格
        List<String> vaildSheetNameList = validateExcelTable(dataMap);
        //两条多线程处理
        ExecutorService executorService = Executors.newFixedThreadPool(2);
        try {
            Future<?> insertDbFuture = executorService.submit(() -> insertCaseDB(vaildSheetNameList)); //TODO:后续DB逻辑放到后端
            Future<?> renderTableFuture = executorService.submit(() -> renderTable(dataMap, manualImport));

            // 等待两个任务完成
            insertDbFuture.get();
            renderTableFuture.get();
        } catch (InterruptedException | java.util.concurrent.ExecutionException e) {
            log.error("处理Excel数据时发生错误: {}", e.getMessage(), e);
            // 根据需要处理异常，例如向用户显示错误消息
            Thread.currentThread().interrupt(); // 重置中断状态
        } finally {
            executorService.shutdown();
        }
    }

    private void insertCaseDB(List<String> sheetNameList) {
        //插入Excel数据
        OperationTargetHolder.getExcelKit().insertExcelCaseData(sheetNameList);
    }

    public void renderTable(Map<String, ExcelSheetData> dataMap, boolean manualImport) {
        //清理变量
        syncMap.clear();
        tableMap.clear();
        excelCaseTabPaneView.setExcelCaseFilePath();
        //遍历表格数据
        for (Map.Entry<String, ExcelSheetData> entry : dataMap.entrySet()) {
            String sheetName = entry.getKey();
            log.info("正在渲染Excel表格：{}", sheetName);
            ExcelSheetData excelSheetData = entry.getValue();
            ExcelCaseTable table = new ExcelCaseTable(mainModel, clientView, excelCaseTabPaneView);  //创建ExcelCaseTable实例
            if (!excelSheetData.isValid()) {
                //格式不符合要求
                JScrollPane autoScrollPane = table.addAutoScrollRowHeader(1, ADJUST_WIDTH);
                autoScrollPane.setViewportView(new CopyableLabel(String.format("该用例表格式不符合，请重新检查表头是否包含动作序列: %s", ExcelCaseTemplate.OfficialSequenceHeader.getHeader())));
                autoScrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_ALWAYS);
                autoScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
                addTab(sheetName, autoScrollPane);
            } else {
                List<String> tableHeaderList = excelSheetData.getTableHeader();
                long st = System.currentTimeMillis();
                // 计算用例条数
//            int caseCount = excelSheetData.getTableData().size();
//            String displayName = sheetName + "(" + caseCount + ")";
                table.setSheetName(sheetName);
                syncMap.put(table, tableHeaderList);
                tableMap.put(sheetName, table);
                tableHeaderList.add(0, SELECT_ALL_TEXT); // 添加全选列
                List<HashMap<Integer, String>> tableData = excelSheetData.getTableData();
                ((DefaultTableModel) table.getModel()).setDataVector(new Object[0][tableHeaderList.size()], tableHeaderList.toArray(new String[0]));
                table.getTableHeader().getColumnModel().getColumn(0).setMaxWidth(50);
                table.renderRows(tableData);
                syncTableConfig(table, manualImport, entry, sheetName);
                CaseConfigJsonManager.getCaseHeaderBySheetName(sheetName).setTableHeaderDropDownOptions(excelSheetData.getTableHeaderDropDownOptions());
                CaseConfigJsonManager.syncExcelCaseConfigFile();
                log.info("表格{}渲染耗时:{}s", sheetName, (System.currentTimeMillis() - st) / 1000.0);
            }
        }
        mainModel.getTestCaseTableModel().testCaseLoaded();
    }

    public void renderTableComplete() {
        if (!syncMap.isEmpty()) {
            //获取map的第一个entry
            Map.Entry<ExcelCaseTable, List<String>> entry = syncMap.entrySet().iterator().next();
            entry.getKey().syncExcelCaseTemplateConfig(entry.getValue());
            OperationTargetHolder.getExcelKit().syncColumnConstantsTemplate(ColumnNameConstants.getInstance());
            log.info("导入Excel案例成功");
        }
    }


    public void syncTableConfig(ExcelCaseTable table, boolean manualImport, Map.Entry<String, ExcelSheetData> entry, String sheetName) {
        if (table == null || entry == null || entry.getValue() == null || sheetName == null) {
            throw new IllegalArgumentException("syncTableConfig参数不能为空");
        }
        try {
            if (manualImport) {
                syncCaseContentList(table, sheetName, entry.getValue());
            }
            AutoScrollPane autoScrollPane = new AutoScrollPane(table);
//            JScrollPane autoScrollPane =  table.addAutoScrollRowHeader(1, ADJUST_WIDTH);
            table.setScrollPane(autoScrollPane);
            FrozenColumnTable frozenColumnTable = new FrozenColumnTable(mainModel, 0, autoScrollPane);
            autoScrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_ALWAYS);
            autoScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
            autoScrollPane.getVerticalScrollBar().setUI(new BasicScrollBarUI());
            addTab(sheetName, autoScrollPane);
            table.setFrozenTable(frozenColumnTable);
            showTableConfig(sheetName, table);
            table.setSync(true);
            table.setRendering(false);
            table.setEnabled(true);
        } catch (Exception e) {
            // 记录异常日志
            log.error("同步Excel配置报错：{}", e.getMessage());
            table.setRendering(false);
            table.setEnabled(true);
        }
    }

    private static void showTableConfig(String sheetName, ExcelCaseTable table) {
        boolean isAllSelected = true;
        //显示表头设置列表、列宽、行选中
        CaseContent caseHeader = CaseConfigJson.getInstance().getCaseContentBySheetName(sheetName);
        if (caseHeader != null) {
            List<CaseHeaderContent> headerConfigList = caseHeader.getCaseHeaderContentList();
            table.syncTableCaseHeader(headerConfigList);
            //每次加载Excel后，都需要到同步excelCaseConfig设置
            List<CaseRowContent> caseRowContentList = caseHeader.getCaseRowContentList();
            if (CollectionUtils.isNotEmpty(caseRowContentList)) {
                for (int i = 0; i < caseRowContentList.size(); i++) {
                    boolean selected = caseRowContentList.get(i).isSelected();
                    if (!selected) {
                        isAllSelected = false;
                    }
                    table.setRowSelected(i, caseRowContentList.get(i).isSelected());
                }
                if (isAllSelected) {
                    //已经解决了全选复选框没选中的问题，因为table被套了一层frozenColumnTable，导致之前的调用的方法不生效了
                    table.getFrozenColumnTable().programmableSelectAll(true);
//                    table.programmableSelectAll(true);
                }
            }
        }
    }


    /**
     * 创建表头内容列表
     *
     * @param tableHeaderList 表头列表
     * @return 表头内容列表
     */
    public static List<CaseHeaderContent> createTableHeaderData(JTable table, List<String> tableHeaderList) {
//        List<String> defualtHeaderList = DefaultTableHeaderConfig.getInstance().getHeaderList();
        Map<String, Integer> headerMap = DefaultTableHeaderConfig.getInstance().getHeaderMap();
        List<CaseHeaderContent> list = new ArrayList<>();
        String commonElement = findCommonElement(tableHeaderList, new ArrayList<>(headerMap.keySet()));  //判断是否是BIC的默认显示列，ICI3全显示
        for (int i = 0; i < tableHeaderList.size(); i++) {
            String columnName = tableHeaderList.get(i);
            CaseHeaderContent content = new CaseHeaderContent();
            content.setColumnName(columnName);
            content.setColumnIndex(i);
            content.setModelIndex(i);
            content.setVisible(commonElement == null || headerMap.containsKey(columnName));
            content.setColumnWidth(commonElement == null ? HEADER_DEFAULT_COLUMN_WIDTH :
                    (headerMap.getOrDefault(columnName, HEADER_DEFAULT_COLUMN_WIDTH)));  //固定宽度
            content.setBuiltin(false);
            if (isBuiltinTableHeaderColumn(columnName)) {
                content.setBuiltin(true);
            }
            list.add(content);
        }
        return list;
    }


    @Deprecated
    public static boolean defaultShowTableHeaderColumn(String columnName) {
        return columnName.equals("NO\n编号") || columnName.equals("TC_ID\n案例编号") || columnName.equals("TestKey\n测试功能点")
                || columnName.equals("Initial_Condition\n初始条件") || columnName.equals("Action\n动作") || columnName.equals("Expected_Result\n预期结果")
                || columnName.equals("InitTestSequences\n初始条件序列") || columnName.equals("ActionTestSequences\n操作步骤序列")
                || columnName.equals("ExpectedTestSequences\n预期结果序列") || columnName.equals("Actual_Result\n实际结果");
    }

    public static boolean isBuiltinTableHeaderColumn(String columnName) {
        return columnName.equals("id") || columnName.equals("uuid") || columnName.equals("tableName");
    }

    public void selectTable(ExcelCaseTable excelCaseTable) {
        setSelectedComponent(excelCaseTable.getScrollPane());
    }


    /**
     * 在界面上加载用例时，同步表头设置列表和行选中列表设置
     * TODO:继续优化代码
     */
    public void syncCaseContentList(ExcelCaseTable table, String sheetName, ExcelSheetData excelSheetData) {
        //新加载用例表时，对比配置文件中的表头配置，如果相同，则不更新
        CaseContent excelCaseContent = CaseConfigJsonManager.getCaseHeaderBySheetName(sheetName);
        if (excelCaseContent == null) {
            List<CaseHeaderContent> caseHeaderContentList = createTableHeaderData(table, excelSheetData.getTableHeader());
            excelCaseContent = new CaseContent();
            excelCaseContent.setSheetName(sheetName);
            excelCaseContent.setCaseHeaderContentList(caseHeaderContentList);
        }
        List<CaseHeaderContent> caseHeaderContentList = excelCaseContent.getCaseHeaderContentList();
        List<CaseRowContent> caseRowContentList = new ArrayList<>();
        int executedSelectColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getChoose());
        int testScenarioColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestScenario());

        if (executedSelectColumnId != -1) {
            for (int i = 0; i < table.getRowCount(); i++) {
                //手动加载用例时，按是否选中测试字段去渲染复选框
                Object executedSelected = table.getModel().getValueAt(i, executedSelectColumnId);
                boolean isSelected = ObjectUtils.defaultIfNull(executedSelected, "").toString().equals("YES");
                //系统测试的导入用例勾选需求，不仅要看selected，还要看testScenario列是否为台架测试，才需要勾选
                if (testScenarioColumnId != -1) {
                    Object testScenario = table.getModel().getValueAt(i, testScenarioColumnId);
                    if (!ObjectUtils.defaultIfNull(testScenario, "").toString().equals("Automated Test")) {
                        isSelected = false;
                    }
                }
                CaseRowContent caseRowContent = new CaseRowContent();
                caseRowContent.setRowId(i);
                caseRowContent.setSelected(isSelected);
                caseRowContentList.add(caseRowContent);
            }
            excelCaseContent.setCaseRowContentList(caseRowContentList);
        } else {
            for (int i = 0; i < table.getRowCount(); i++) {
                CaseRowContent caseRowContent = new CaseRowContent();
                caseRowContent.setRowId(i);
                caseRowContent.setSelected(true);
                caseRowContentList.add(caseRowContent);
            }
            excelCaseContent.setCaseRowContentList(caseRowContentList);
        }
        if (CollectionUtils.isNotEmpty(CaseConfigJson.getInstance().getExcelCaseContentList())) {
            List<CaseContent> excelCaseContentList = CaseConfigJson.getInstance().getExcelCaseContentList();
            int existSheet = -1;  //用来表示不存在sheetName的标志位
            for (int i = 0; i < excelCaseContentList.size(); i++) {
                if (sheetName.equals(excelCaseContentList.get(i).getSheetName())) {
                    existSheet = i;
                    break;
                }
            }
            if (existSheet == -1) {
                CaseConfigJson.getInstance().getExcelCaseContentList().add(excelCaseContent);
            } else {
                CaseConfigJson.getInstance().getExcelCaseContentList().get(existSheet).setCaseRowContentList(caseRowContentList);
                //新表与配置文件做匹配，新表遍历配置文件，相同列名的会按配置文件的CaseHeaderContent，否则会取新表的CaseHeaderContent
                List<CaseHeaderContent> caseHeaderContentList1 = CaseConfigJson.getInstance().getExcelCaseContentList().get(existSheet).getCaseHeaderContentList();
//                Map<String, CaseHeaderContent> headerContentMap = caseHeaderContentList1.stream()
//                        .collect(Collectors.toMap(CaseHeaderContent::getColumnName, c -> c));
                Map<Integer, CaseHeaderContent> headerContentMap = caseHeaderContentList1.stream()
                        .collect(Collectors.toMap(CaseHeaderContent::getColumnIndex, c -> c));
                for (CaseHeaderContent content : caseHeaderContentList) {
                    CaseHeaderContent contentFromList = headerContentMap.get(content.getColumnName());
                    if (contentFromList != null) {
                        content.setVisible(contentFromList.isVisible());
                        content.setColumnWidth(contentFromList.getColumnWidth());
                    }
                }
                CaseConfigJson.getInstance().getExcelCaseContentList().get(existSheet).setCaseHeaderContentList(caseHeaderContentList);
            }
        } else {
            List<CaseContent> caseContentList = new ArrayList<>();
            excelCaseContent.setCaseHeaderContentList(caseHeaderContentList);
            caseContentList.add(excelCaseContent);
            CaseConfigJson.getInstance().setExcelCaseContentList(caseContentList);
        }
        CaseConfigJsonManager.syncExcelCaseConfigFile();
    }

    /**
     * 同步更新表头设置
     *
     * @param caseHeaderContentList 表头内容列表
     */
    public void syncCaseHeaderContentList(List<CaseHeaderContent> caseHeaderContentList) {
        //对于已存在的表，只更新不更新行选中列表的记录
        CaseContent excelCaseContent = CaseConfigJsonManager.getCaseHeaderBySheetName(getSelectedSheetName());
        if (excelCaseContent != null) {
            excelCaseContent.setCaseHeaderContentList(caseHeaderContentList);
        }
        CaseConfigJsonManager.syncExcelCaseConfigFile();
    }


    /**
     * 渲染表格
     *
     * @param caseHeaderConfigList 表头设置
     */
    //add by haiyu
    public void renderTableByHeaderConfig(List<CaseHeaderContent> caseHeaderConfigList, ExcelCaseTable table) {
        if (table != null) {
            table.renderColumns(caseHeaderConfigList);
        }
    }

    public void renderTableByHeaderConfig(ExcelCaseTable table) {
        CaseContent caseContentBySheetName = CaseConfigJson.getInstance().getCaseContentBySheetName(table.getSheetName());
        if (caseContentBySheetName != null) {
            List<CaseHeaderContent> caseHeaderContentList = caseContentBySheetName.getCaseHeaderContentList();
            table.renderColumns(caseHeaderContentList);
        }
    }

    /**
     * 获取当前excel case面板
     *
     * @return ExcelCaseTable
     */
    public ExcelCaseTable getSelectedExcelCaseTable() {
        ExcelCaseTable table = null;
        JScrollPane selectedComponent = (JScrollPane) getSelectedComponent();
        if (selectedComponent != null) {
            JViewport view = selectedComponent.getViewport();
            Component[] components = view.getComponents();
            for (Component component : components) {
                if (component instanceof JTable) {
                    table = (ExcelCaseTable) component;
                }
            }
        }
        return table;
    }

    /**
     * 检查tabPane是否存在
     *
     * @param tabbedTitle 标题
     * @return tabPane是否存在
     */
    public boolean checkTabbedPaneExist(String tabbedTitle) {
        return indexOfTab(tabbedTitle) != -1;
    }

    /**
     * 移除tabPane
     *
     * @param tabbedTitle 标题
     */
    public void removeTabbedPane(String tabbedTitle) {
        if (indexOfTab(tabbedTitle) != -1) {
            remove(indexOfTab(tabbedTitle));
        }
    }

    /**
     * 移除所有tabPane
     */
    public void removeAllTabbedPane() {
        removeAll();
    }

    /**
     * 获取所有tabPane的标题
     *
     * @return 所有tabPane的标题
     */
    public List<String> getAllTabbedPaneTitle() {
        List<String> allTabList = new ArrayList<>();
        int tabCount = getTabCount();
        for (int i = 0; i < tabCount; i++) {
            String titleAt = getTitleAt(i);
            allTabList.add(titleAt);
        }
        return allTabList;
    }

    public String getValueByColumnName(int row, String columnName) {
        JTable table = getSelectedExcelCaseTable();
        return (String) table.getModel().getValueAt(row, SwingUtil.getColumnIndex(table, columnName));
    }


    public Map<String, ExcelCaseTable> getAllDisplayTableMap() {
        List<String> allTabbedPaneTitleList = getAllTabbedPaneTitle();
        Map<String, ExcelCaseTable> map = new LinkedHashMap<>();
        for (String title : allTabbedPaneTitleList) {
            if (tableMap.containsKey(title)) {
                map.put(title, tableMap.get(title));
            }
        }
        return map;
    }

    public static boolean tableFormatCorrect(List<String> tableHeaderList) {
        return findCommonElement(tableHeaderList,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("initTestSequences")) != null &&
                findCommonElement(tableHeaderList,
                        ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("actionTestSequences")) != null &&
                findCommonElement(tableHeaderList,
                        ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("expectedTestSequences")) != null;
    }


    /**
     * 获取当前选择的sheetName名称
     *
     * @return sheetName名称
     */
    public String getSelectedSheetName() {
        return getTitleAt(getSelectedIndex());
    }

}
