package ui.layout.left.display.components.container.can;

import cn.hutool.core.collection.CollectionUtil;
import com.desaysv.CANDbcPanel;
import com.desaysv.receivepanel.callback.CanDbcReceiveReturnListener;
import com.desaysv.receivepanel.model.R.CANMessageR;
import com.desaysv.receivepanel.model.S.CANMessage;
import com.desaysv.receivepanel.model.S.CANMessageDeserializer;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import javafx.embed.swing.JFXPanel;
import lombok.extern.slf4j.Slf4j;
import sdk.base.SseSession;
import sdk.base.operation.Operation;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.domain.bus.*;
import sdk.entity.CanDevice;
import sdk.entity.ResistanceDevice;
import ui.base.BaseView;
import ui.layout.left.display.components.container.can.model.DbcPathModel;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.util.List;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;

@Slf4j
public class CanDbcReceiveSettingView extends BaseCanDbcSettingView implements DbcFileObserver, BaseView {

    private final String savePath;
    private final CanContainer canContainer;
    private final SseSession sseSession;
    private final ExecutorService executorService;
    private final BlockingQueue<String> messageQueue;
    private final BlockingQueue<String> messageListQueue;
    private List<String> dbcPaths;
    private CANDbcPanel canPanel;
    private MainModel mainModel;
    private boolean skipControllerUpdate = false; //防止重复调用标识
    private static final Gson gson = new GsonBuilder()
            .registerTypeAdapter(CANMessage.class, new CANMessageDeserializer())
            .create();

    public CanDbcReceiveSettingView(CanContainer canContainer, MainModel mainModel, CanConfig canConfig, int channel, String savePath, DbcPathModel model) {
        super(canContainer, channel, canConfig, model);
        this.savePath = savePath;
        this.mainModel = mainModel;
        this.canContainer = canContainer;
        DbcConfig dbcConfig = canConfig.getDbcConfigs().get(channel + "");
        if (dbcConfig != null) {
            this.dbcPaths = dbcConfig.getDbcPaths();
        }
        DbcFileManager.getInstance().addObserver(channel, canContainer.getDevice().getDeviceName(), this);
        sseSession = new SseSession();
        JFXPanel jfxPanel = new JFXPanel();
        centerPanel.add(jfxPanel, BorderLayout.CENTER);
        messageQueue = new LinkedBlockingQueue<>(1000);
        messageListQueue = new LinkedBlockingQueue<>(1000);
        executorService = Executors.newFixedThreadPool(4, r -> {
            Thread thread = new Thread(r);
            thread.setName(String.format("CanDbcReceiveThread-%d-%d", channel, thread.getId()));
            return thread;
        });
        createView();
    }

    @Override
    public void createView() {
        canPanel = new CANDbcPanel(channel, savePath, dbcPaths, new CanDbcReceiveReturnListener() {

            @Override
            public void stop() {
                ((CanDevice) canContainer.getDevice()).stopDbcReceiver(channel);
            }

            @Override
            public void start() {
                ((CanDevice) canContainer.getDevice()).startDbcReceiver(channel, new DbcConfig(dbcPaths));
            }

            @Override
            public void onSelectDbcFile(List<String> filePaths) {
                skipControllerUpdate = true;
                updateUIComponents(filePaths);
                DbcFileManager.getInstance().notifyObservers(channel, canContainer.getDevice().getDeviceName(), filePaths);
            }

            @Override
            public void onSendMessage(CANMessageR canMessageR) {
                ((CanDevice) canContainer.getDevice()).sendCanMessage(channel, toMessage(canMessageR));

            }

            @Override
            public void stopSendMessage(CANMessageR canMessageR) {
                ((CanDevice) canContainer.getDevice()).stopCanMessage(channel, Integer.decode(canMessageR.getIdHex()));

            }

            @Override
            public void stopSendMessages(List<CANMessageR> canMessageRList) {
                for (CANMessageR msg : canMessageRList) {
                    ((CanDevice) canContainer.getDevice()).stopCanMessage(channel, Integer.decode(msg.getIdHex()));
                }
            }

            @Override
            public void startRealTimeDataForDbc(String filePath, String filterType, String filterText) {
                ((CanDevice) canContainer.getDevice()).startRealTimeData(channel, new CanMessageRealTimeSave(filePath, filterType, filterText));
            }

            @Override
            public void stopRealTimeDataForDbc() {
                ((CanDevice) canContainer.getDevice()).stopRealTimeData(channel);
            }

            ResistanceDevice resistanceDevice;

            @Override
            public boolean registerResistanceDevice() {
                //FIXME:获取连接的设备
                Set<Device> devices = Device.getConnectedDevices("resistanceType");
                if (CollectionUtil.isNotEmpty(devices)) {
                    resistanceDevice = (ResistanceDevice) devices.toArray()[0];
                    return true;
                } else {
                    SwingUtil.showWarningDialog(null, "请先添加电阻板卡设备！");
                    return false;
                }
            }

            @Override
            public void resistanceValue(int resistance) {
                resistanceDevice.sendResistanceValue(resistance);
            }

            @Override
            public void addStartSaveToScript(String filePath, String fileType, String filterType,String filterText){
                Operation operation = Operation.buildOperation(((CanDevice) canContainer.getDevice()));
                operation.getOperationTarget().setChannel(channel);
                operation.setOperationObject(new CanLogRealTimeSaveParameter(filePath, fileType, filterType, filterText, new DbcConfig(dbcPaths)));
                operation.setFriendlyOperationObject("开始实时抓取canLog(DBC)");
                operation.setOperationMethod(DeviceMethods.startCaptureDbcCanLog);
                mainModel.getOperationModel().updateOperation(operation);
            }

            @Override
            public void addStopSaveToScript(){
                Operation operation = Operation.buildOperation(((CanDevice) canContainer.getDevice()));
                operation.getOperationTarget().setChannel(channel);
                operation.setFriendlyOperationObject("结束实时抓取canLog(DBC)");
                operation.setOperationMethod(DeviceMethods.stopCaptureDbcCanLog);
                mainModel.getOperationModel().updateOperation(operation);

            }
        });
        add(canPanel);
        // 启动一个线程来处理单个消息队列中的消息
        executorService.submit(() -> {
            while (true) {
                try {
                    String originalString = messageQueue.take();
                    handleSSEMessage(originalString);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });
        // 启动一个线程来处理列表消息队列中的消息
        executorService.submit(() -> {
            while (true) {
                try {
                    String originalString = messageListQueue.take();
                    handleSSEMessageList(originalString);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });

        // 提交任务来读取SSE流
        executorService.submit(() -> {
            try {
                // NICAN
                sseSession.readStream(UrlConstants.getSseUrl("canDbcReceiver" + channel),
                        originalString -> {
                            try {
                                messageQueue.put(originalString);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                        });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        executorService.submit(() -> {
            try {
                // TSCAN
                sseSession.readStream(UrlConstants.getSseUrl("canDbcReceiverList" + channel),
                        originalString -> {
                            try {
                                messageListQueue.put(originalString);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                        });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });

    }

    private void handleSSEMessage(String originalString) {
        try {
            String json = originalString.substring(originalString.indexOf("data:") + 5).trim();
            CANMessage messages = gson.fromJson(json, CANMessage.class);
            if (messages.getChn() != null && Integer.parseInt(messages.getChn()) == channel) {
                canPanel.updateData(messages);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private void handleSSEMessageList(String originalString) {
        try {
            String json = originalString.substring(originalString.indexOf("data:") + 5).trim();
            json = json.substring(1, json.length() - 1);
            json = json.replace("\\\"", "\""); // 将转义的双引号转换回普通双引号
            List<CANMessage> messages = gson.fromJson(json, new TypeToken<List<CANMessage>>() {
            }.getType());
            messages.removeIf(msg -> Integer.parseInt(msg.getChn()) != channel); // 过滤掉不属于当前channel的消息
            if (!messages.isEmpty()) {
                canPanel.updateData(messages);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private CanMessage toMessage(CANMessageR msg) {
        CanMessage canMessage = new CanMessage();
        canMessage.setDlc(msg.getDlc());
        canMessage.setCanFd("CANFD".equals(msg.getEventType()) || "CANFD加速".equals(msg.getEventType()));
        canMessage.setData(msg.getData());
        canMessage.setChannel(Integer.valueOf(msg.getChn()));
        canMessage.setArbitrationId(Integer.decode(msg.getIdHex()));
        canMessage.setSendMethod("正常发送");
        canMessage.setSendTimes(msg.getSendCount() == 0 ? -1 : msg.getSendCount());
        canMessage.setPeriod(msg.getInterval() / 1000.0f);
        return canMessage;
    }

    public void dispose() {
        executorService.shutdownNow();
        sseSession.closeAll();
        DbcFileManager.getInstance().removeObserver(channel, canContainer.getDevice().getDeviceName(), this);
        model.removeListener(this::updateUIComponents);
    }

    @Override
    protected void onLoadDbcClicked(int index, String path) {
        SwingUtilities.invokeLater(() -> {
            canPanel.loadDBC(index, path);
        });
        //需要通知另一个面板添加DBC路径
        skipControllerUpdate = true;
        DbcFileManager.getInstance().notifyObservers(channel, canContainer.getDevice().getDeviceName(), getDbcPaths());
    }

    @Override
    protected void removeLastDbc(int index) {
        SwingUtilities.invokeLater(() -> {
            canPanel.removeDBC(index);
        });
        skipControllerUpdate = true;
        DbcFileManager.getInstance().notifyObservers(channel, canContainer.getDevice().getDeviceName(), getDbcPaths());
    }

    @Override
    public void onDbcFileChanged(List<String> dbcFilePaths) {
        try {
            dbcPaths = dbcFilePaths;
            if (!skipControllerUpdate && canPanel != null) {
                SwingUtilities.invokeLater(() -> canPanel.changeDBCPath(dbcFilePaths));
            }
        } finally {
            skipControllerUpdate = false;
        }
    }
}
