import com.formdev.flatlaf.FlatIntelliJLaf;
import common.constant.AppConstants;
import common.utils.CommandUtils;
import common.utils.ComputerInfo;
import common.utils.NetworkUtils;
import lombok.extern.slf4j.Slf4j;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.domain.Client;
import sdk.entity.OperationTargetHolder;
import sdk.entity.HealthKit;
import sdk.entity.VerifyKit;
import ui.base.AppInfo;
import ui.base.BackgroundRobotKeyboardListener;
import ui.config.xml.app.AppConfiguration;
import ui.entry.ClientView;
import ui.layout.login.LoginDialog;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-8 11:40
 * @description :
 * @modified By :
 * @since : 2022-4-8
 */
@Slf4j
public class MainApp {

    public static void main(String[] args) {
        int serverPort = 61535;
        int clientPort = 62535;
        // 检测是否有其他实例正在运行
        SocketInstanceControl socketInstanceControl = new SocketInstanceControl(clientPort);

        // 检查是否已经检测到另一个实例，如果是，等待用户输入
        socketInstanceControl.checkInstanceRunning(serverPort, "后端");
        socketInstanceControl.checkInstanceRunning(clientPort, "前端");

        // 启动Socket检测服务
        socketInstanceControl.start();

        // 继续运行程序
        runApplication(args);
    }

    private static void runApplication(String[] args) {
        log.info("客户端编译日期:{}", AppConstants.COMPILE_DATE);
        if (args.length > 0) {
            AppConstants.setBaseComputerIp(args[0]);
            log.debug("客户端启动参数:{}", Arrays.toString(args));
        }
        String instanceId = System.getProperty("flytest.instance.id", "未设置");
        log.info("实例ID: {}", instanceId);
        EventQueue.invokeLater(MainApp::run);
    }

    private static void autoLoginAndRun(MainModel mainModel,Client client) {
        try {
            UIManager.setLookAndFeel(new FlatIntelliJLaf());
            AppInfo appInfoFormXml =AppConfiguration.getInstance().getAppConfig().getAppInfo();
            //拼接一个字符串 获取电脑名/账户名
            String testUnitInfo=null;
            String userName = System.getProperty("user.name");
            try {
                InetAddress localhost = InetAddress.getLocalHost();
                String hostName = localhost.getHostName();
                testUnitInfo = hostName +"/"+userName;
            } catch (UnknownHostException e) {
                log.info("获取电脑名失败");
            }
            if (testUnitInfo!=null){
                appInfoFormXml.setTestUnit(testUnitInfo);
            }
            mainModel.setAppInfo(appInfoFormXml);
            mainModel.getAppInfo().setClient(client.getName());
            mainModel.getAppInfo().setAppVersion(client.getVersion());
            mainModel.getAppInfo().setServerPid(HealthKit.getServerPid());
            //TODO:每次切换账户要改变
            BaseHttpClient.appInfo = mainModel.getAppInfo();
            OperationTargetHolder.getTestClientKit().loginUser(mainModel.getAppInfo());
            new UIMonitorThread().start();
            // 启动主界面
            new ClientView(mainModel);
        } catch (Exception e) {
            log.error("自动登录失败", e);
            JOptionPane.showMessageDialog(null, "自动登录配置错误: " + e.getMessage());
            System.exit(1);
        }
        log.info("启动成功");
    }

    private static void run() {
//            new ServerView().bootUp();
        log.info("等待服务端完全启动...");
        VerifyKit.verifyFromServer();
        log.info("服务端初始化成功");
        Client client = new Client();
        client.setName(AppConstants.APP_NAME);
        ComputerInfo computerInfo = NetworkUtils.getComputerInfo();
        client.setPcName(computerInfo.getComputerName());
        client.setAddr("");
        client.setPid(CommandUtils.getAppPID());
        JsonResponse<Client> response = OperationTargetHolder.getTestClientKit().registerClient(client);
//            System.out.println("computerInfo.getComputerName():"+computerInfo.getComputerName());
//            System.out.println("client:"+client);
        if (response.isOk()) {
            try {
                UIManager.setLookAndFeel(new FlatIntelliJLaf());
//                UIManager.put("Table.gridColor", Color.GRAY); // 将网格线颜色设置为灰色
                UIManager.put("TabbedPane.tabHeight", 20); // 设置为tabPane高度
                UIManager.put("Table.selectionInactiveBackground", UIManager.getColor("Table.selectionBackground"));
                UIManager.put("Table.selectionInactiveForeground", UIManager.getColor("Table.selectionForeground"));
                UIManager.put("SplitPaneDivider.draggingColor", Color.GRAY);
                UIManager.put("SplitPaneDivider.border", BorderFactory.createLineBorder(Color.GRAY));
                // 启用抗锯齿
                System.setProperty("awt.useSystemAAFontSettings", "on");
                System.setProperty("swing.aatext", "true");
            } catch (UnsupportedLookAndFeelException e) {
                log.warn(e.getMessage(), e);
            }
            client = response.getData();
            log.info("启动客户端:{}", client.getVersion());
            MainModel mainModel = new MainModel();
            mainModel.getAppInfo().setClient(client.getName());
            mainModel.getAppInfo().setAppVersion(client.getVersion());
            mainModel.getAppInfo().setServerPid(HealthKit.getServerPid());
            //TODO:每次切换账户要改变
            BaseHttpClient.appInfo = mainModel.getAppInfo();
            BackgroundRobotKeyboardListener listener = new BackgroundRobotKeyboardListener(mainModel);
            listener.startListening();

            // 添加环境变量检查
            String skipLogin = System.getenv("FLYTEST_SKIP_LOGIN");
//            String skipLogin = "true";
            if ("true".equalsIgnoreCase(skipLogin)) {
                log.info("检测到跳过登录环境变量，直接启动主界面");
                Client finalClient = client;
                EventQueue.invokeLater(() -> autoLoginAndRun(mainModel, finalClient));
            } else {
                LoginDialog loginDialog = new LoginDialog(mainModel);
                if (loginDialog.isLogin()) {
                    OperationTargetHolder.getTestClientKit().loginUser(mainModel.getAppInfo());
                    new UIMonitorThread().start();
                    new ClientView(mainModel);
                }
            }
        }
    }
}
