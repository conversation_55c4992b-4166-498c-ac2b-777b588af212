package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.constants.UrlConstants;
import sdk.domain.action_sequence.*;
import sdk.entity.interfaces.IActionSequence;
import ui.model.mail.TestResultReportDto;

import java.util.ArrayList;
import java.util.List;

public class ActionSequenceKit extends BaseHttpClient implements IActionSequence {

    @Override
    public JsonResponse<ActionSequenceCheckReporter> checkActionSequenceGrammar(ActionSequenceContext actionSequenceContext) {
        return defaultPostJsonResponse(UrlConstants.ActionSequenceUrls.CHECK_ACTION_SEQUENCE, actionSequenceContext, new TypeReference<JsonResponse<ActionSequenceCheckReporter>>() {
        });
    }

    @Override
    public JsonResponse<ActionSequenceCheckReporter> checkAndExecuteActionSequence(ActionSequenceContext actionSequenceContext) {
        return defaultPostJsonResponse(UrlConstants.ActionSequenceUrls.CHECK_ADN_EXECUTE_ACTION_SEQUENCE, actionSequenceContext, new TypeReference<JsonResponse<ActionSequenceCheckReporter>>() {
        });
    }

    @Override
    public JsonResponse<String> prepareExecuteActionSequence(ActionSequenceStatus actionSequenceStatus) {
        return defaultPostJsonResponse(UrlConstants.ActionSequenceUrls.PREPARE_EXECUTE_ACTION_SEQUENCE, actionSequenceStatus, new TypeReference<JsonResponse<String>>() {
        });
    }

    @Override
    public JsonResponse<String> failActionSequence() {
        return defaultGetJsonResponse(UrlConstants.ActionSequenceUrls.FAIL_ACTION_SEQUENCE, new TypeReference<JsonResponse<String>>() {
        });
    }

    @Override
    public JsonResponse<String> pauseActionSequence() {
        return defaultGetJsonResponse(UrlConstants.ActionSequenceUrls.PAUSE_ACTION_SEQUENCE, new TypeReference<JsonResponse<String>>() {
        });
    }

    @Override
    public JsonResponse<String> resumeActionSequence() {
        return defaultGetJsonResponse(UrlConstants.ActionSequenceUrls.RESUME_ACTION_SEQUENCE, new TypeReference<JsonResponse<String>>() {
        });
    }

    @Override
    public JsonResponse<String> stopActionSequence() {
        return defaultGetJsonResponse(UrlConstants.ActionSequenceUrls.STOP_ACTION_SEQUENCE, new TypeReference<JsonResponse<String>>() {
        });
    }

    @Override
    public JsonResponse<String> completeActionSequence(ActionSequenceStatus actionSequenceStatus) {
        return defaultPostJsonResponse(UrlConstants.ActionSequenceUrls.COMPLETE_ACTION_SEQUENCE, actionSequenceStatus, new TypeReference<JsonResponse<String>>() {
        });
    }

    @Override
    public JsonResponse<String> closeAllSSE() {
        return defaultPostJsonResponse(UrlConstants.ActionSequenceUrls.CLOSE_ALL_SSE, null, new TypeReference<JsonResponse<String>>() {
        });
    }

    @Override
    public JsonResponse<String> executeActionSequence(ActionSequenceContext actionSequenceContext) {
        return defaultPostJsonResponse(UrlConstants.ActionSequenceUrls.EXECUTE_ACTION_SEQUENCE, actionSequenceContext, new TypeReference<JsonResponse<String>>() {
        });
    }

    @Override
    public JsonResponse<String> setMailTestResult(TestResultReportDto testResultReportDto) {
        return defaultPostJsonResponse(UrlConstants.ActionSequenceUrls.MAIL_TEST_RESULT, testResultReportDto, new TypeReference<JsonResponse<String>>() {
        });
    }

    @Override
    public JsonResponse<String> setRobotTestResult(TestResultReportDto testResultReportDto) {
        return defaultPostJsonResponse(UrlConstants.ActionSequenceUrls.ROBOT_TEST_RESULT, testResultReportDto, new TypeReference<JsonResponse<String>>() {
        });
    }

    @Override
    public JsonResponse<String> setCloudDocTestResult(TestResultReportDto testResultReportDto) {
        return defaultPostJsonResponse(UrlConstants.ActionSequenceUrls.CLOUD_DOC_TEST_RESULT, testResultReportDto, new TypeReference<JsonResponse<String>>() {
        });
    }

    @Override
    public JsonResponse<ActionSequenceCheckReporter> checkAndExecuteSingleActionSequence(ActionSequenceContext actionSequenceContext) {
        return defaultPostJsonResponse(UrlConstants.ActionSequenceUrls.CHECK_ADN_EXECUTE_SINGLE_ACTION_SEQUENCE, actionSequenceContext, new TypeReference<JsonResponse<ActionSequenceCheckReporter>>() {
        });
    }

    @Override
    public JsonResponse<String> stopSingleActionSequence() {
        return defaultGetJsonResponse(UrlConstants.ActionSequenceUrls.STOP_SINGLE_ACTION_SEQUENCE, new TypeReference<JsonResponse<String>>() {
        });
    }

    @Override
    public List<String> convertActionSequence(ActionSequenceSimpleContext actionSequenceContext) {
        JsonResponse<List<String>> resp = defaultPostJsonResponse(UrlConstants.ActionSequenceUrls.CONVERT_ACTION_SEQUENCE, actionSequenceContext, new TypeReference<JsonResponse<List<String>>>() {
        });
        return resp.isOk() ? resp.getData() : new ArrayList<>();
    }

    @Override
    public JsonResponse<String> updateTestConfig(ActionSequenceTestConfig actionSequenceTestConfig) {
        return defaultPostJsonResponse(
                UrlConstants.ActionSequenceUrls.UPDATE_TEST_CONFIG,
                actionSequenceTestConfig,
                new TypeReference<JsonResponse<String>>() {
                }
        );
    }

    @Override
    public JsonResponse<ActionSequenceTestConfig> readTestConfig(String projectName) {
        return defaultGetJsonResponse(
                String.format(UrlConstants.ActionSequenceUrls.READ_TEST_CONFIG, projectName),
                new TypeReference<JsonResponse<ActionSequenceTestConfig>>() {
                }
        );
    }

}
