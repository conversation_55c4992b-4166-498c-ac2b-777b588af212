package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.entity.interfaces.IAndroid;

import java.util.List;

/**
 * Android设备
 */
public class AndroidDevice extends DefaultVisionDevice implements IAndroid {

    public AndroidDevice() {
        super(DeviceModel.Android.USB_ANDROID);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.ANDROID.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_ANDROID;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<AndroidDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<AndroidDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<AndroidDevice>> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllAndroids(getDeviceModel()),
                new TypeReference<JsonResponse<List<AndroidDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }

    @Override
    public OperationResult screenshot() {
        return callOperationMethod(DeviceMethods.screenshot);
    }

    public OperationResult executeAdbCommand(String command) {
        return callOperationMethod(DeviceMethods.executeADBCommand, command);
    }
}
