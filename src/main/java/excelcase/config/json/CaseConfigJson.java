package excelcase.config.json;

import excelcase.exportcase.CaseReportFileConfig;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/3/28 10:06
 * @description :
 * @modified By :
 * @since : 2023/3/28
 **/
@Data
public class CaseConfigJson {
    private volatile static CaseConfigJson caseConfigJson;
    private String originalExcelCaseFilePath;
    private String templateExcelCaseFilePath;
    private String exportExcelCaseFilePath;
    private String logFilePath;
    private Integer testMode = 0;
    private int testTimes = 1;
    //    private List<String> allCaseNameList;
//    private Object[] selectedCaseNameList;
    private LinkedHashMap<String, Boolean> selectedSheetMap;
    //包含所有excel的header
    private List<CaseContent> excelCaseContentList;
    private int headerRowNumber; //表头行数
    private int rowExecuteIntervalTime; //执行间隔时间
    private boolean filterTableHeader = false;

    public static CaseConfigJson getInstance() {
        if (caseConfigJson == null) {
            synchronized (CaseReportFileConfig.class) {
                if (caseConfigJson == null) {
                    caseConfigJson = new CaseConfigJson();
                }
            }
        }
        return caseConfigJson;
    }

    public CaseContent getCaseContentBySheetName(String sheetName) {
        if (sheetName.isEmpty() || excelCaseContentList == null)
            return null;
        return excelCaseContentList.stream().filter(caseContent -> caseContent.getSheetName().equals(sheetName)).findFirst().orElse(null);
    }

}
